[{"D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\index.js": "1", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\App.js": "2", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\toolbar.js": "3", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\ui.js": "4", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\submit.js": "5", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\draggableNode.js": "6", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\store.js": "7", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\inputNode.js": "8", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\outputNode.js": "9", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\textNode.js": "10", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\llmNode.js": "11", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\BaseNode.js": "12", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\switchNode.js": "13", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\mathNode.js": "14", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\filterNode.js": "15", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\aggregatorNode.js": "16", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\timerNode.js": "17", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\styles\\theme.js": "18", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\NodeInput.js": "19", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\Modal.js": "20", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\CustomDropdown.js": "21"}, {"size": 254, "mtime": 1750274986338, "results": "22", "hashOfConfig": "23"}, {"size": 572, "mtime": 1750360123549, "results": "24", "hashOfConfig": "23"}, {"size": 3851, "mtime": 1750364510526, "results": "25", "hashOfConfig": "23"}, {"size": 6126, "mtime": 1750360123536, "results": "26", "hashOfConfig": "23"}, {"size": 4136, "mtime": 1750314956052, "results": "27", "hashOfConfig": "23"}, {"size": 621, "mtime": 1750285171720, "results": "28", "hashOfConfig": "23"}, {"size": 1353, "mtime": 1750285195771, "results": "29", "hashOfConfig": "23"}, {"size": 1460, "mtime": 1750369055789, "results": "30", "hashOfConfig": "23"}, {"size": 1473, "mtime": 1750369067200, "results": "31", "hashOfConfig": "23"}, {"size": 6554, "mtime": 1750368221041, "results": "32", "hashOfConfig": "23"}, {"size": 762, "mtime": 1750352626197, "results": "33", "hashOfConfig": "23"}, {"size": 5286, "mtime": 1750368201849, "results": "34", "hashOfConfig": "23"}, {"size": 1429, "mtime": 1750369032589, "results": "35", "hashOfConfig": "23"}, {"size": 1789, "mtime": 1750368999597, "results": "36", "hashOfConfig": "23"}, {"size": 1786, "mtime": 1750369011610, "results": "37", "hashOfConfig": "23"}, {"size": 2530, "mtime": 1750369022075, "results": "38", "hashOfConfig": "23"}, {"size": 2654, "mtime": 1750423184736, "results": "39", "hashOfConfig": "23"}, {"size": 2138, "mtime": 1750279121739, "results": "40", "hashOfConfig": "23"}, {"size": 718, "mtime": 1750286504985, "results": "41", "hashOfConfig": "23"}, {"size": 3508, "mtime": 1750314892016, "results": "42", "hashOfConfig": "23"}, {"size": 3820, "mtime": 1750368381812, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "47"}, "159wf7k", {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "47"}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "47"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "47"}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "47"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "63"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "47"}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "47"}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "47"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "47"}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "47"}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\index.js", [], [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\App.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\toolbar.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\ui.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\submit.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\draggableNode.js", [], [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\store.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\inputNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\outputNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\textNode.js", ["109"], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\llmNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\BaseNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\switchNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\mathNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\filterNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\aggregatorNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\timerNode.js", ["110"], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\styles\\theme.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\NodeInput.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\Modal.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\CustomDropdown.js", ["111", "112"], [], {"ruleId": "113", "severity": 1, "message": "114", "line": 71, "column": 6, "nodeType": "115", "endLine": 71, "endColumn": 34, "suggestions": "116"}, {"ruleId": "117", "severity": 1, "message": "118", "line": 5, "column": 36, "nodeType": "119", "messageId": "120", "endLine": 5, "endColumn": 52}, {"ruleId": "121", "severity": 1, "message": "122", "line": 38, "column": 7, "nodeType": "123", "messageId": "124", "endLine": 61, "endColumn": 8}, {"ruleId": "113", "severity": 1, "message": "125", "line": 68, "column": 6, "nodeType": "115", "endLine": 68, "endColumn": 37, "suggestions": "126"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'calculateSize'. Either include it or remove the dependency array.", "ArrayExpression", ["127"], "no-unused-vars", "'inlineLabelStyle' is defined but never used.", "Identifier", "unusedVar", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useEffect has a missing dependency: 'handleOptionSelect'. Either include it or remove the dependency array.", ["128"], {"desc": "129", "fix": "130"}, {"desc": "131", "fix": "132"}, "Update the dependencies array to be: [calculateSize, currText, variables.length]", {"range": "133", "text": "134"}, "Update the dependencies array to be: [isOpen, focusedIndex, options, handleOptionSelect]", {"range": "135", "text": "136"}, [2432, 2460], "[calculateSize, currText, variables.length]", [1852, 1883], "[isOpen, focusedIndex, options, handleOptionSelect]"]