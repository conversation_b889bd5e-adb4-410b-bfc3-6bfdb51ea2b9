{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\components\\\\CustomDropdown.js\",\n  _s = $RefreshSig$();\n// CustomDropdown.js\n// Custom dropdown component with smooth animations and custom arrow\n\nimport { useState, useRef, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const CustomDropdown = ({\n  value,\n  onChange,\n  options = [],\n  placeholder = \"Select option\",\n  className = \"\",\n  style = {},\n  disabled = false\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [focusedIndex, setFocusedIndex] = useState(-1);\n  const dropdownRef = useRef(null);\n  const optionsRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n        setFocusedIndex(-1);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Handle keyboard navigation\n  useEffect(() => {\n    const handleKeyDown = event => {\n      if (!isOpen) return;\n      switch (event.key) {\n        case 'ArrowDown':\n          event.preventDefault();\n          setFocusedIndex(prev => prev < options.length - 1 ? prev + 1 : 0);\n          break;\n        case 'ArrowUp':\n          event.preventDefault();\n          setFocusedIndex(prev => prev > 0 ? prev - 1 : options.length - 1);\n          break;\n        case 'Enter':\n          event.preventDefault();\n          if (focusedIndex >= 0) {\n            handleOptionSelect(options[focusedIndex]);\n          }\n          break;\n        case 'Escape':\n          setIsOpen(false);\n          setFocusedIndex(-1);\n          break;\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      return () => document.removeEventListener('keydown', handleKeyDown);\n    }\n  }, [isOpen, focusedIndex, options]);\n  const handleToggle = () => {\n    if (!disabled) {\n      setIsOpen(!isOpen);\n      setFocusedIndex(-1);\n    }\n  };\n  const handleOptionSelect = option => {\n    onChange({\n      target: {\n        value: option.value\n      }\n    });\n    setIsOpen(false);\n    setFocusedIndex(-1);\n  };\n  const selectedOption = options.find(opt => opt.value === value);\n  const displayText = selectedOption ? selectedOption.label : placeholder;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: dropdownRef,\n    className: `custom-dropdown ${className} ${isOpen ? 'open' : ''} ${disabled ? 'disabled' : ''}`,\n    style: style,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dropdown-trigger\",\n      onClick: handleToggle,\n      tabIndex: disabled ? -1 : 0,\n      onKeyDown: e => {\n        if (e.key === 'Enter' || e.key === ' ') {\n          e.preventDefault();\n          handleToggle();\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"dropdown-text\",\n        children: displayText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `dropdown-arrow ${isOpen ? 'rotated' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"12\",\n          height: \"8\",\n          viewBox: \"0 0 12 8\",\n          fill: \"none\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M1 1.5L6 6.5L11 1.5\",\n            stroke: \"currentColor\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: optionsRef,\n      className: `dropdown-options ${isOpen ? 'visible' : ''}`,\n      children: options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `dropdown-option ${option.value === value ? 'selected' : ''} ${index === focusedIndex ? 'focused' : ''}`,\n        onClick: () => handleOptionSelect(option),\n        onMouseEnter: () => setFocusedIndex(index),\n        children: option.label\n      }, option.value, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(CustomDropdown, \"Yl6xfYj5IOSYltMndZL6It0wl0Q=\");\n_c = CustomDropdown;\nvar _c;\n$RefreshReg$(_c, \"CustomDropdown\");", "map": {"version": 3, "names": ["useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "CustomDropdown", "value", "onChange", "options", "placeholder", "className", "style", "disabled", "_s", "isOpen", "setIsOpen", "focusedIndex", "setFocusedIndex", "dropdownRef", "optionsRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleKeyDown", "key", "preventDefault", "prev", "length", "handleOptionSelect", "handleToggle", "option", "selectedOption", "find", "opt", "displayText", "label", "ref", "children", "onClick", "tabIndex", "onKeyDown", "e", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "map", "index", "onMouseEnter", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/components/CustomDropdown.js"], "sourcesContent": ["// CustomDropdown.js\n// Custom dropdown component with smooth animations and custom arrow\n\nimport { useState, useRef, useEffect } from 'react';\n\nexport const CustomDropdown = ({ \n  value, \n  onChange, \n  options = [], \n  placeholder = \"Select option\",\n  className = \"\",\n  style = {},\n  disabled = false \n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [focusedIndex, setFocusedIndex] = useState(-1);\n  const dropdownRef = useRef(null);\n  const optionsRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n        setFocusedIndex(-1);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Handle keyboard navigation\n  useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (!isOpen) return;\n\n      switch (event.key) {\n        case 'ArrowDown':\n          event.preventDefault();\n          setFocusedIndex(prev => \n            prev < options.length - 1 ? prev + 1 : 0\n          );\n          break;\n        case 'ArrowUp':\n          event.preventDefault();\n          setFocusedIndex(prev => \n            prev > 0 ? prev - 1 : options.length - 1\n          );\n          break;\n        case 'Enter':\n          event.preventDefault();\n          if (focusedIndex >= 0) {\n            handleOptionSelect(options[focusedIndex]);\n          }\n          break;\n        case 'Escape':\n          setIsOpen(false);\n          setFocusedIndex(-1);\n          break;\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      return () => document.removeEventListener('keydown', handleKeyDown);\n    }\n  }, [isOpen, focusedIndex, options]);\n\n  const handleToggle = () => {\n    if (!disabled) {\n      setIsOpen(!isOpen);\n      setFocusedIndex(-1);\n    }\n  };\n\n  const handleOptionSelect = (option) => {\n    onChange({ target: { value: option.value } });\n    setIsOpen(false);\n    setFocusedIndex(-1);\n  };\n\n  const selectedOption = options.find(opt => opt.value === value);\n  const displayText = selectedOption ? selectedOption.label : placeholder;\n\n  return (\n    <div \n      ref={dropdownRef}\n      className={`custom-dropdown ${className} ${isOpen ? 'open' : ''} ${disabled ? 'disabled' : ''}`}\n      style={style}\n    >\n      <div \n        className=\"dropdown-trigger\"\n        onClick={handleToggle}\n        tabIndex={disabled ? -1 : 0}\n        onKeyDown={(e) => {\n          if (e.key === 'Enter' || e.key === ' ') {\n            e.preventDefault();\n            handleToggle();\n          }\n        }}\n      >\n        <span className=\"dropdown-text\">{displayText}</span>\n        <div className={`dropdown-arrow ${isOpen ? 'rotated' : ''}`}>\n          <svg width=\"12\" height=\"8\" viewBox=\"0 0 12 8\" fill=\"none\">\n            <path \n              d=\"M1 1.5L6 6.5L11 1.5\" \n              stroke=\"currentColor\" \n              strokeWidth=\"1.5\" \n              strokeLinecap=\"round\" \n              strokeLinejoin=\"round\"\n            />\n          </svg>\n        </div>\n      </div>\n      \n      <div \n        ref={optionsRef}\n        className={`dropdown-options ${isOpen ? 'visible' : ''}`}\n      >\n        {options.map((option, index) => (\n          <div\n            key={option.value}\n            className={`dropdown-option ${\n              option.value === value ? 'selected' : ''\n            } ${\n              index === focusedIndex ? 'focused' : ''\n            }`}\n            onClick={() => handleOptionSelect(option)}\n            onMouseEnter={() => setFocusedIndex(index)}\n          >\n            {option.label}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAC7BC,KAAK;EACLC,QAAQ;EACRC,OAAO,GAAG,EAAE;EACZC,WAAW,GAAG,eAAe;EAC7BC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAMkB,WAAW,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMkB,UAAU,GAAGlB,MAAM,CAAC,IAAI,CAAC;;EAE/B;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIH,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtET,SAAS,CAAC,KAAK,CAAC;QAChBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACrB;IACF,CAAC;IAEDQ,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlB,SAAS,CAAC,MAAM;IACd,MAAM0B,aAAa,GAAIP,KAAK,IAAK;MAC/B,IAAI,CAACP,MAAM,EAAE;MAEb,QAAQO,KAAK,CAACQ,GAAG;QACf,KAAK,WAAW;UACdR,KAAK,CAACS,cAAc,CAAC,CAAC;UACtBb,eAAe,CAACc,IAAI,IAClBA,IAAI,GAAGvB,OAAO,CAACwB,MAAM,GAAG,CAAC,GAAGD,IAAI,GAAG,CAAC,GAAG,CACzC,CAAC;UACD;QACF,KAAK,SAAS;UACZV,KAAK,CAACS,cAAc,CAAC,CAAC;UACtBb,eAAe,CAACc,IAAI,IAClBA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAGvB,OAAO,CAACwB,MAAM,GAAG,CACzC,CAAC;UACD;QACF,KAAK,OAAO;UACVX,KAAK,CAACS,cAAc,CAAC,CAAC;UACtB,IAAId,YAAY,IAAI,CAAC,EAAE;YACrBiB,kBAAkB,CAACzB,OAAO,CAACQ,YAAY,CAAC,CAAC;UAC3C;UACA;QACF,KAAK,QAAQ;UACXD,SAAS,CAAC,KAAK,CAAC;UAChBE,eAAe,CAAC,CAAC,CAAC,CAAC;UACnB;MACJ;IACF,CAAC;IAED,IAAIH,MAAM,EAAE;MACVW,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEE,aAAa,CAAC;MACnD,OAAO,MAAMH,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEC,aAAa,CAAC;IACrE;EACF,CAAC,EAAE,CAACd,MAAM,EAAEE,YAAY,EAAER,OAAO,CAAC,CAAC;EAEnC,MAAM0B,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtB,QAAQ,EAAE;MACbG,SAAS,CAAC,CAACD,MAAM,CAAC;MAClBG,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMgB,kBAAkB,GAAIE,MAAM,IAAK;IACrC5B,QAAQ,CAAC;MAAEiB,MAAM,EAAE;QAAElB,KAAK,EAAE6B,MAAM,CAAC7B;MAAM;IAAE,CAAC,CAAC;IAC7CS,SAAS,CAAC,KAAK,CAAC;IAChBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAMmB,cAAc,GAAG5B,OAAO,CAAC6B,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAChC,KAAK,KAAKA,KAAK,CAAC;EAC/D,MAAMiC,WAAW,GAAGH,cAAc,GAAGA,cAAc,CAACI,KAAK,GAAG/B,WAAW;EAEvE,oBACEL,OAAA;IACEqC,GAAG,EAAEvB,WAAY;IACjBR,SAAS,EAAG,mBAAkBA,SAAU,IAAGI,MAAM,GAAG,MAAM,GAAG,EAAG,IAAGF,QAAQ,GAAG,UAAU,GAAG,EAAG,EAAE;IAChGD,KAAK,EAAEA,KAAM;IAAA+B,QAAA,gBAEbtC,OAAA;MACEM,SAAS,EAAC,kBAAkB;MAC5BiC,OAAO,EAAET,YAAa;MACtBU,QAAQ,EAAEhC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAE;MAC5BiC,SAAS,EAAGC,CAAC,IAAK;QAChB,IAAIA,CAAC,CAACjB,GAAG,KAAK,OAAO,IAAIiB,CAAC,CAACjB,GAAG,KAAK,GAAG,EAAE;UACtCiB,CAAC,CAAChB,cAAc,CAAC,CAAC;UAClBI,YAAY,CAAC,CAAC;QAChB;MACF,CAAE;MAAAQ,QAAA,gBAEFtC,OAAA;QAAMM,SAAS,EAAC,eAAe;QAAAgC,QAAA,EAAEH;MAAW;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpD9C,OAAA;QAAKM,SAAS,EAAG,kBAAiBI,MAAM,GAAG,SAAS,GAAG,EAAG,EAAE;QAAA4B,QAAA,eAC1DtC,OAAA;UAAK+C,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,GAAG;UAACC,OAAO,EAAC,UAAU;UAACC,IAAI,EAAC,MAAM;UAAAZ,QAAA,eACvDtC,OAAA;YACEmD,CAAC,EAAC,qBAAqB;YACvBC,MAAM,EAAC,cAAc;YACrBC,WAAW,EAAC,KAAK;YACjBC,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9C,OAAA;MACEqC,GAAG,EAAEtB,UAAW;MAChBT,SAAS,EAAG,oBAAmBI,MAAM,GAAG,SAAS,GAAG,EAAG,EAAE;MAAA4B,QAAA,EAExDlC,OAAO,CAACoD,GAAG,CAAC,CAACzB,MAAM,EAAE0B,KAAK,kBACzBzD,OAAA;QAEEM,SAAS,EAAG,mBACVyB,MAAM,CAAC7B,KAAK,KAAKA,KAAK,GAAG,UAAU,GAAG,EACvC,IACCuD,KAAK,KAAK7C,YAAY,GAAG,SAAS,GAAG,EACtC,EAAE;QACH2B,OAAO,EAAEA,CAAA,KAAMV,kBAAkB,CAACE,MAAM,CAAE;QAC1C2B,YAAY,EAAEA,CAAA,KAAM7C,eAAe,CAAC4C,KAAK,CAAE;QAAAnB,QAAA,EAE1CP,MAAM,CAACK;MAAK,GATRL,MAAM,CAAC7B,KAAK;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrC,EAAA,CApIWR,cAAc;AAAA0D,EAAA,GAAd1D,cAAc;AAAA,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}