{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\filterNode.js\",\n  _s = $RefreshSig$();\n// filterNode.js\n// Demonstrates data filtering with configurable conditions\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { CustomDropdown } from '../components/CustomDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const FilterNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [condition, setCondition] = useState((data === null || data === void 0 ? void 0 : data.condition) || 'contains');\n  const [value, setValue] = useState((data === null || data === void 0 ? void 0 : data.value) || '');\n  const handleConditionChange = e => {\n    setCondition(e.target.value);\n  };\n  const handleValueChange = e => {\n    setValue(e.target.value);\n  };\n  const conditionOptions = [{\n    value: 'contains',\n    label: 'Contains'\n  }, {\n    value: 'equals',\n    label: 'Equals'\n  }, {\n    value: 'startsWith',\n    label: 'Starts with'\n  }];\n  const handles = [createHandle(`${id}-input`, 'target', Position.Left), createHandle(`${id}-filtered`, 'source', Position.Right, {\n    top: '30%'\n  }), createHandle(`${id}-rejected`, 'source', Position.Right, {\n    top: '70%'\n  })];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Filter\",\n    handles: handles,\n    nodeType: \"filter\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Condition:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: condition,\n          onChange: handleConditionChange,\n          className: \"node-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"contains\",\n            children: \"Contains\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"equals\",\n            children: \"Equals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"startsWith\",\n            children: \"Starts with\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Value:\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: value,\n          onChange: handleValueChange,\n          className: \"node-input\",\n          placeholder: \"Enter filter value\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(FilterNode, \"w8eFE1CGItfZ9+5TtbJeH3kfyts=\");\n_c = FilterNode;\nvar _c;\n$RefreshReg$(_c, \"FilterNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "commonLabelStyle", "Position", "CustomDropdown", "jsxDEV", "_jsxDEV", "FilterNode", "id", "data", "_s", "condition", "setCondition", "value", "setValue", "handleConditionChange", "e", "target", "handleValueChange", "conditionOptions", "label", "handles", "Left", "Right", "top", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "onChange", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/filterNode.js"], "sourcesContent": ["// filterNode.js\n// Demonstrates data filtering with configurable conditions\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { CustomDropdown } from '../components/CustomDropdown';\n\nexport const FilterNode = ({ id, data }) => {\n  const [condition, setCondition] = useState(data?.condition || 'contains');\n  const [value, setValue] = useState(data?.value || '');\n\n  const handleConditionChange = (e) => {\n    setCondition(e.target.value);\n  };\n\n  const handleValueChange = (e) => {\n    setValue(e.target.value);\n  };\n\n  const conditionOptions = [\n    { value: 'contains', label: 'Contains' },\n    { value: 'equals', label: 'Equals' },\n    { value: 'startsWith', label: 'Starts with' }\n  ];\n\n  const handles = [\n    createHandle(`${id}-input`, 'target', Position.Left),\n    createHandle(`${id}-filtered`, 'source', Position.Right, { top: '30%' }),\n    createHandle(`${id}-rejected`, 'source', Position.Right, { top: '70%' })\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Filter\"\n      handles={handles}\n      nodeType=\"filter\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        <label style={commonLabelStyle}>\n          Condition:\n          <select\n            value={condition}\n            onChange={handleConditionChange}\n            className=\"node-input\"\n          >\n            <option value=\"contains\">Contains</option>\n            <option value=\"equals\">Equals</option>\n            <option value=\"startsWith\">Starts with</option>\n          </select>\n        </label>\n        <label style={commonLabelStyle}>\n          Value:\n          <input\n            type=\"text\"\n            value={value}\n            onChange={handleValueChange}\n            className=\"node-input\"\n            placeholder=\"Enter filter value\"\n          />\n        </label>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AACrE,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,cAAc,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMC,UAAU,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,UAAU,CAAC;EACzE,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,KAAK,KAAI,EAAE,CAAC;EAErD,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMK,iBAAiB,GAAIF,CAAC,IAAK;IAC/BF,QAAQ,CAACE,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMM,gBAAgB,GAAG,CACvB;IAAEN,KAAK,EAAE,UAAU;IAAEO,KAAK,EAAE;EAAW,CAAC,EACxC;IAAEP,KAAK,EAAE,QAAQ;IAAEO,KAAK,EAAE;EAAS,CAAC,EACpC;IAAEP,KAAK,EAAE,YAAY;IAAEO,KAAK,EAAE;EAAc,CAAC,CAC9C;EAED,MAAMC,OAAO,GAAG,CACdpB,YAAY,CAAE,GAAEO,EAAG,QAAO,EAAE,QAAQ,EAAEL,QAAQ,CAACmB,IAAI,CAAC,EACpDrB,YAAY,CAAE,GAAEO,EAAG,WAAU,EAAE,QAAQ,EAAEL,QAAQ,CAACoB,KAAK,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACxEvB,YAAY,CAAE,GAAEO,EAAG,WAAU,EAAE,QAAQ,EAAEL,QAAQ,CAACoB,KAAK,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,CACzE;EAED,oBACElB,OAAA,CAACN,QAAQ;IACPQ,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXgB,KAAK,EAAC,QAAQ;IACdJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,QAAQ;IAAAC,QAAA,eAEjBrB,OAAA;MAAKsB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnErB,OAAA;QAAOsB,KAAK,EAAE1B,gBAAiB;QAAAyB,QAAA,GAAC,YAE9B,eAAArB,OAAA;UACEO,KAAK,EAAEF,SAAU;UACjBqB,QAAQ,EAAEjB,qBAAsB;UAChCkB,SAAS,EAAC,YAAY;UAAAN,QAAA,gBAEtBrB,OAAA;YAAQO,KAAK,EAAC,UAAU;YAAAc,QAAA,EAAC;UAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1C/B,OAAA;YAAQO,KAAK,EAAC,QAAQ;YAAAc,QAAA,EAAC;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC/B,OAAA;YAAQO,KAAK,EAAC,YAAY;YAAAc,QAAA,EAAC;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACR/B,OAAA;QAAOsB,KAAK,EAAE1B,gBAAiB;QAAAyB,QAAA,GAAC,QAE9B,eAAArB,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAEA,KAAM;UACbmB,QAAQ,EAAEd,iBAAkB;UAC5Be,SAAS,EAAC,YAAY;UACtBM,WAAW,EAAC;QAAoB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAC3B,EAAA,CA1DWH,UAAU;AAAAiC,EAAA,GAAVjC,UAAU;AAAA,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}