{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\inputNode.js\",\n  _s = $RefreshSig$();\n// inputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';\nimport { NodeInput } from '../components/NodeInput';\nimport { CustomDropdown } from '../components/CustomDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const InputNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [currName, setCurrName] = useState((data === null || data === void 0 ? void 0 : data.inputName) || id.replace('customInput-', 'input_'));\n  const [inputType, setInputType] = useState(data.inputType || 'Text');\n  const handleNameChange = e => {\n    setCurrName(e.target.value);\n  };\n  const handleTypeChange = e => {\n    setInputType(e.target.value);\n  };\n  const typeOptions = [{\n    value: 'Text',\n    label: 'Text'\n  }, {\n    value: 'File',\n    label: 'File'\n  }];\n  const handles = [HANDLE_CONFIGS.sourceRight(`${id}-value`)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Input\",\n    handles: handles,\n    nodeType: \"input\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Name:\", /*#__PURE__*/_jsxDEV(NodeInput, {\n          value: currName,\n          onChange: handleNameChange,\n          placeholder: \"Enter input name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Type:\", /*#__PURE__*/_jsxDEV(CustomDropdown, {\n          value: inputType,\n          onChange: handleTypeChange,\n          options: typeOptions,\n          className: \"node-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(InputNode, \"K/fVZ6JMb9mTcAC2LWPy4yI2M3A=\");\n_c = InputNode;\nvar _c;\n$RefreshReg$(_c, \"InputNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "HANDLE_CONFIGS", "commonLabelStyle", "NodeInput", "CustomDropdown", "jsxDEV", "_jsxDEV", "InputNode", "id", "data", "_s", "currName", "setCurrName", "inputName", "replace", "inputType", "setInputType", "handleNameChange", "e", "target", "value", "handleTypeChange", "typeOptions", "label", "handles", "sourceRight", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "onChange", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "options", "className", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/inputNode.js"], "sourcesContent": ["// inputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';\nimport { NodeInput } from '../components/NodeInput';\nimport { CustomDropdown } from '../components/CustomDropdown';\n\nexport const InputNode = ({ id, data }) => {\n  const [currName, setCurrName] = useState(data?.inputName || id.replace('customInput-', 'input_'));\n  const [inputType, setInputType] = useState(data.inputType || 'Text');\n\n  const handleNameChange = (e) => {\n    setCurrName(e.target.value);\n  };\n\n  const handleTypeChange = (e) => {\n    setInputType(e.target.value);\n  };\n\n  const typeOptions = [\n    { value: 'Text', label: 'Text' },\n    { value: 'File', label: 'File' }\n  ];\n\n  const handles = [HANDLE_CONFIGS.sourceRight(`${id}-value`)];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Input\"\n      handles={handles}\n      nodeType=\"input\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        <label style={commonLabelStyle}>\n          Name:\n          <NodeInput\n            value={currName}\n            onChange={handleNameChange}\n            placeholder=\"Enter input name\"\n          />\n        </label>\n        <label style={commonLabelStyle}>\n          Type:\n          <CustomDropdown\n            value={inputType}\n            onChange={handleTypeChange}\n            options={typeOptions}\n            className=\"node-input\"\n          />\n        </label>\n      </div>\n    </BaseNode>\n  );\n}\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,YAAY;AACvE,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,cAAc,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMC,SAAS,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,SAAS,KAAIL,EAAE,CAACM,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;EACjG,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAACU,IAAI,CAACM,SAAS,IAAI,MAAM,CAAC;EAEpE,MAAME,gBAAgB,GAAIC,CAAC,IAAK;IAC9BN,WAAW,CAACM,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9BF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAME,WAAW,GAAG,CAClB;IAAEF,KAAK,EAAE,MAAM;IAAEG,KAAK,EAAE;EAAO,CAAC,EAChC;IAAEH,KAAK,EAAE,MAAM;IAAEG,KAAK,EAAE;EAAO,CAAC,CACjC;EAED,MAAMC,OAAO,GAAG,CAACvB,cAAc,CAACwB,WAAW,CAAE,GAAEjB,EAAG,QAAO,CAAC,CAAC;EAE3D,oBACEF,OAAA,CAACN,QAAQ;IACPQ,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXiB,KAAK,EAAC,OAAO;IACbF,OAAO,EAAEA,OAAQ;IACjBG,QAAQ,EAAC,OAAO;IAAAC,QAAA,eAEhBtB,OAAA;MAAKuB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnEtB,OAAA;QAAOuB,KAAK,EAAE3B,gBAAiB;QAAA0B,QAAA,GAAC,OAE9B,eAAAtB,OAAA,CAACH,SAAS;UACRiB,KAAK,EAAET,QAAS;UAChBsB,QAAQ,EAAEhB,gBAAiB;UAC3BiB,WAAW,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRhC,OAAA;QAAOuB,KAAK,EAAE3B,gBAAiB;QAAA0B,QAAA,GAAC,OAE9B,eAAAtB,OAAA,CAACF,cAAc;UACbgB,KAAK,EAAEL,SAAU;UACjBkB,QAAQ,EAAEZ,gBAAiB;UAC3BkB,OAAO,EAAEjB,WAAY;UACrBkB,SAAS,EAAC;QAAY;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAA5B,EAAA,CAhDYH,SAAS;AAAAkC,EAAA,GAATlC,SAAS;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}