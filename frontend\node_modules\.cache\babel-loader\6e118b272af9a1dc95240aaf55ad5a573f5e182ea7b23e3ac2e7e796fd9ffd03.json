{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\mathNode.js\",\n  _s = $RefreshSig$();\n// mathNode.js\n// Demonstrates mathematical operations with multiple inputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { CustomDropdown } from '../components/CustomDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const MathNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [operation, setOperation] = useState((data === null || data === void 0 ? void 0 : data.operation) || 'add');\n  const handleOperationChange = e => {\n    setOperation(e.target.value);\n  };\n  const operationOptions = [{\n    value: 'add',\n    label: 'Add'\n  }, {\n    value: 'subtract',\n    label: 'Subtract'\n  }, {\n    value: 'multiply',\n    label: 'Multiply'\n  }, {\n    value: 'divide',\n    label: 'Divide'\n  }];\n  const handles = [createHandle(`${id}-input1`, 'target', Position.Left, {\n    top: '25%'\n  }), createHandle(`${id}-input2`, 'target', Position.Left, {\n    top: '75%'\n  }), createHandle(`${id}-result`, 'source', Position.Right)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Math\",\n    handles: handles,\n    nodeType: \"math\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Operation:\", /*#__PURE__*/_jsxDEV(CustomDropdown, {\n          value: operation,\n          onChange: handleOperationChange,\n          options: operationOptions,\n          className: \"node-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center',\n          padding: '4px 8px',\n          backgroundColor: 'rgba(138, 43, 226, 0.1)',\n          borderRadius: '4px',\n          border: '1px solid rgba(138, 43, 226, 0.2)'\n        },\n        children: [\"Performs \", operation, \" operation\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(MathNode, \"R7FWnHhCWCtLxw0vmLO8tIvK/QI=\");\n_c = MathNode;\nvar _c;\n$RefreshReg$(_c, \"MathNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "commonLabelStyle", "Position", "CustomDropdown", "jsxDEV", "_jsxDEV", "MathNode", "id", "data", "_s", "operation", "setOperation", "handleOperationChange", "e", "target", "value", "operationOptions", "label", "handles", "Left", "top", "Right", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "onChange", "options", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "color", "fontStyle", "textAlign", "padding", "backgroundColor", "borderRadius", "border", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/mathNode.js"], "sourcesContent": ["// mathNode.js\n// Demonstrates mathematical operations with multiple inputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { CustomDropdown } from '../components/CustomDropdown';\n\nexport const MathNode = ({ id, data }) => {\n  const [operation, setOperation] = useState(data?.operation || 'add');\n\n  const handleOperationChange = (e) => {\n    setOperation(e.target.value);\n  };\n\n  const operationOptions = [\n    { value: 'add', label: 'Add' },\n    { value: 'subtract', label: 'Subtract' },\n    { value: 'multiply', label: 'Multiply' },\n    { value: 'divide', label: 'Divide' }\n  ];\n\n  const handles = [\n    createHandle(`${id}-input1`, 'target', Position.Left, { top: '25%' }),\n    createHandle(`${id}-input2`, 'target', Position.Left, { top: '75%' }),\n    createHandle(`${id}-result`, 'source', Position.Right)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Math\"\n      handles={handles}\n      nodeType=\"math\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        <label style={commonLabelStyle}>\n          Operation:\n          <CustomDropdown\n            value={operation}\n            onChange={handleOperationChange}\n            options={operationOptions}\n            className=\"node-input\"\n          />\n        </label>\n        <div style={{\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center',\n          padding: '4px 8px',\n          backgroundColor: 'rgba(138, 43, 226, 0.1)',\n          borderRadius: '4px',\n          border: '1px solid rgba(138, 43, 226, 0.2)'\n        }}>\n          Performs {operation} operation\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AACrE,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,cAAc,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,KAAK,CAAC;EAEpE,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,gBAAgB,GAAG,CACvB;IAAED,KAAK,EAAE,KAAK;IAAEE,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAEF,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAW,CAAC,EACxC;IAAEF,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAW,CAAC,EACxC;IAAEF,KAAK,EAAE,QAAQ;IAAEE,KAAK,EAAE;EAAS,CAAC,CACrC;EAED,MAAMC,OAAO,GAAG,CACdlB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACiB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrEpB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACiB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrEpB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACmB,KAAK,CAAC,CACvD;EAED,oBACEhB,OAAA,CAACN,QAAQ;IACPQ,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXc,KAAK,EAAC,MAAM;IACZJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,MAAM;IAAAC,QAAA,eAEfnB,OAAA;MAAKoB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnEnB,OAAA;QAAOoB,KAAK,EAAExB,gBAAiB;QAAAuB,QAAA,GAAC,YAE9B,eAAAnB,OAAA,CAACF,cAAc;UACbY,KAAK,EAAEL,SAAU;UACjBmB,QAAQ,EAAEjB,qBAAsB;UAChCkB,OAAO,EAAEd,gBAAiB;UAC1Be,SAAS,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACR9B,OAAA;QAAKoB,KAAK,EAAE;UACVW,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,0BAA0B;UACjCC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,QAAQ;UACnBC,OAAO,EAAE,SAAS;UAClBC,eAAe,EAAE,yBAAyB;UAC1CC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,GAAC,WACQ,EAACd,SAAS,EAAC,YACtB;MAAA;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAC1B,EAAA,CArDWH,QAAQ;AAAAsC,EAAA,GAARtC,QAAQ;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}