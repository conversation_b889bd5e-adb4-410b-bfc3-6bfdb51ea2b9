{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\aggregatorNode.js\",\n  _s = $RefreshSig$();\n// aggregatorNode.js\n// Demonstrates data aggregation with multiple inputs and operations\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { NodeInput } from '../components/NodeInput';\nimport { CustomDropdown } from '../components/CustomDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AggregatorNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [operation, setOperation] = useState((data === null || data === void 0 ? void 0 : data.operation) || 'concat');\n  const [separator, setSeparator] = useState((data === null || data === void 0 ? void 0 : data.separator) || ', ');\n  const handleOperationChange = e => {\n    setOperation(e.target.value);\n  };\n  const handleSeparatorChange = e => {\n    setSeparator(e.target.value);\n  };\n  const operationOptions = [{\n    value: 'concat',\n    label: 'Concat'\n  }, {\n    value: 'sum',\n    label: 'Sum'\n  }, {\n    value: 'avg',\n    label: 'Average'\n  }, {\n    value: 'max',\n    label: 'Max'\n  }, {\n    value: 'min',\n    label: 'Min'\n  }];\n  const handles = [createHandle(`${id}-input1`, 'target', Position.Left, {\n    top: '20%'\n  }), createHandle(`${id}-input2`, 'target', Position.Left, {\n    top: '40%'\n  }), createHandle(`${id}-input3`, 'target', Position.Left, {\n    top: '60%'\n  }), createHandle(`${id}-input4`, 'target', Position.Left, {\n    top: '80%'\n  }), createHandle(`${id}-result`, 'source', Position.Right)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Aggregator\",\n    handles: handles,\n    nodeType: \"aggregator\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Operation:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: operation,\n          onChange: handleOperationChange,\n          className: \"node-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"concat\",\n            children: \"Concat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"sum\",\n            children: \"Sum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"avg\",\n            children: \"Average\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"max\",\n            children: \"Max\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"min\",\n            children: \"Min\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), operation === 'concat' && /*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Separator:\", /*#__PURE__*/_jsxDEV(NodeInput, {\n          value: separator,\n          onChange: handleSeparatorChange,\n          placeholder: \"Enter separator\",\n          style: {\n            fontSize: '11px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center',\n          padding: '4px 8px',\n          backgroundColor: 'rgba(138, 43, 226, 0.1)',\n          borderRadius: '4px',\n          border: '1px solid rgba(138, 43, 226, 0.2)'\n        },\n        children: \"Combines multiple inputs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(AggregatorNode, \"sMY8CXrz1Xg3U6xCkKPfMgyFeGs=\");\n_c = AggregatorNode;\nvar _c;\n$RefreshReg$(_c, \"AggregatorNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "commonLabelStyle", "Position", "NodeInput", "CustomDropdown", "jsxDEV", "_jsxDEV", "AggregatorNode", "id", "data", "_s", "operation", "setOperation", "separator", "setSeparator", "handleOperationChange", "e", "target", "value", "handleSeparatorChange", "operationOptions", "label", "handles", "Left", "top", "Right", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "onChange", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "fontSize", "color", "fontStyle", "textAlign", "padding", "backgroundColor", "borderRadius", "border", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/aggregatorNode.js"], "sourcesContent": ["// aggregatorNode.js\n// Demonstrates data aggregation with multiple inputs and operations\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { NodeInput } from '../components/NodeInput';\nimport { CustomDropdown } from '../components/CustomDropdown';\n\nexport const AggregatorNode = ({ id, data }) => {\n  const [operation, setOperation] = useState(data?.operation || 'concat');\n  const [separator, setSeparator] = useState(data?.separator || ', ');\n\n  const handleOperationChange = (e) => {\n    setOperation(e.target.value);\n  };\n\n  const handleSeparatorChange = (e) => {\n    setSeparator(e.target.value);\n  };\n\n  const operationOptions = [\n    { value: 'concat', label: 'Concat' },\n    { value: 'sum', label: 'Sum' },\n    { value: 'avg', label: 'Average' },\n    { value: 'max', label: '<PERSON>' },\n    { value: 'min', label: 'Min' }\n  ];\n\n  const handles = [\n    createHandle(`${id}-input1`, 'target', Position.Left, { top: '20%' }),\n    createHandle(`${id}-input2`, 'target', Position.Left, { top: '40%' }),\n    createHandle(`${id}-input3`, 'target', Position.Left, { top: '60%' }),\n    createHandle(`${id}-input4`, 'target', Position.Left, { top: '80%' }),\n    createHandle(`${id}-result`, 'source', Position.Right)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Aggregator\"\n      handles={handles}\n      nodeType=\"aggregator\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        <label style={commonLabelStyle}>\n          Operation:\n          <select\n            value={operation}\n            onChange={handleOperationChange}\n            className=\"node-input\"\n          >\n            <option value=\"concat\">Concat</option>\n            <option value=\"sum\">Sum</option>\n            <option value=\"avg\">Average</option>\n            <option value=\"max\">Max</option>\n            <option value=\"min\">Min</option>\n          </select>\n        </label>\n        {operation === 'concat' && (\n          <label style={commonLabelStyle}>\n            Separator:\n            <NodeInput\n              value={separator}\n              onChange={handleSeparatorChange}\n              placeholder=\"Enter separator\"\n              style={{ fontSize: '11px' }}\n            />\n          </label>\n        )}\n        <div style={{\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center',\n          padding: '4px 8px',\n          backgroundColor: 'rgba(138, 43, 226, 0.1)',\n          borderRadius: '4px',\n          border: '1px solid rgba(138, 43, 226, 0.2)'\n        }}>\n          Combines multiple inputs\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AACrE,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,cAAc,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,CAAAW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,QAAQ,CAAC;EACvE,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,CAAAW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,SAAS,KAAI,IAAI,CAAC;EAEnE,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,qBAAqB,GAAIH,CAAC,IAAK;IACnCF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAME,gBAAgB,GAAG,CACvB;IAAEF,KAAK,EAAE,QAAQ;IAAEG,KAAK,EAAE;EAAS,CAAC,EACpC;IAAEH,KAAK,EAAE,KAAK;IAAEG,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAEH,KAAK,EAAE,KAAK;IAAEG,KAAK,EAAE;EAAU,CAAC,EAClC;IAAEH,KAAK,EAAE,KAAK;IAAEG,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAEH,KAAK,EAAE,KAAK;IAAEG,KAAK,EAAE;EAAM,CAAC,CAC/B;EAED,MAAMC,OAAO,GAAG,CACdtB,YAAY,CAAE,GAAEQ,EAAG,SAAQ,EAAE,QAAQ,EAAEN,QAAQ,CAACqB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrExB,YAAY,CAAE,GAAEQ,EAAG,SAAQ,EAAE,QAAQ,EAAEN,QAAQ,CAACqB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrExB,YAAY,CAAE,GAAEQ,EAAG,SAAQ,EAAE,QAAQ,EAAEN,QAAQ,CAACqB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrExB,YAAY,CAAE,GAAEQ,EAAG,SAAQ,EAAE,QAAQ,EAAEN,QAAQ,CAACqB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrExB,YAAY,CAAE,GAAEQ,EAAG,SAAQ,EAAE,QAAQ,EAAEN,QAAQ,CAACuB,KAAK,CAAC,CACvD;EAED,oBACEnB,OAAA,CAACP,QAAQ;IACPS,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXiB,KAAK,EAAC,YAAY;IAClBJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,YAAY;IAAAC,QAAA,eAErBtB,OAAA;MAAKuB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnEtB,OAAA;QAAOuB,KAAK,EAAE5B,gBAAiB;QAAA2B,QAAA,GAAC,YAE9B,eAAAtB,OAAA;UACEY,KAAK,EAAEP,SAAU;UACjBsB,QAAQ,EAAElB,qBAAsB;UAChCmB,SAAS,EAAC,YAAY;UAAAN,QAAA,gBAEtBtB,OAAA;YAAQY,KAAK,EAAC,QAAQ;YAAAU,QAAA,EAAC;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtChC,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAU,QAAA,EAAC;UAAG;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChChC,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAU,QAAA,EAAC;UAAO;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpChC,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAU,QAAA,EAAC;UAAG;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChChC,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAU,QAAA,EAAC;UAAG;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EACP3B,SAAS,KAAK,QAAQ,iBACrBL,OAAA;QAAOuB,KAAK,EAAE5B,gBAAiB;QAAA2B,QAAA,GAAC,YAE9B,eAAAtB,OAAA,CAACH,SAAS;UACRe,KAAK,EAAEL,SAAU;UACjBoB,QAAQ,EAAEd,qBAAsB;UAChCoB,WAAW,EAAC,iBAAiB;UAC7BV,KAAK,EAAE;YAAEW,QAAQ,EAAE;UAAO;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACR,eACDhC,OAAA;QAAKuB,KAAK,EAAE;UACVW,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,0BAA0B;UACjCC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,QAAQ;UACnBC,OAAO,EAAE,SAAS;UAClBC,eAAe,EAAE,yBAAyB;UAC1CC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,EAAC;MAEH;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAC5B,EAAA,CA7EWH,cAAc;AAAAyC,EAAA,GAAdzC,cAAc;AAAA,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}