{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\mathNode.js\",\n  _s = $RefreshSig$();\n// mathNode.js\n// Demonstrates mathematical operations with multiple inputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { CustomDropdown } from '../components/CustomDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const MathNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [operation, setOperation] = useState((data === null || data === void 0 ? void 0 : data.operation) || 'add');\n  const handleOperationChange = e => {\n    setOperation(e.target.value);\n  };\n  const handles = [createHandle(`${id}-input1`, 'target', Position.Left, {\n    top: '25%'\n  }), createHandle(`${id}-input2`, 'target', Position.Left, {\n    top: '75%'\n  }), createHandle(`${id}-result`, 'source', Position.Right)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Math\",\n    handles: handles,\n    nodeType: \"math\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Operation:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: operation,\n          onChange: handleOperationChange,\n          className: \"node-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"add\",\n            children: \"Add\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"subtract\",\n            children: \"Subtract\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"multiply\",\n            children: \"Multiply\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"divide\",\n            children: \"Divide\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center',\n          padding: '4px 8px',\n          backgroundColor: 'rgba(138, 43, 226, 0.1)',\n          borderRadius: '4px',\n          border: '1px solid rgba(138, 43, 226, 0.2)'\n        },\n        children: [\"Performs \", operation, \" operation\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(MathNode, \"R7FWnHhCWCtLxw0vmLO8tIvK/QI=\");\n_c = MathNode;\nvar _c;\n$RefreshReg$(_c, \"MathNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "commonLabelStyle", "Position", "CustomDropdown", "jsxDEV", "_jsxDEV", "MathNode", "id", "data", "_s", "operation", "setOperation", "handleOperationChange", "e", "target", "value", "handles", "Left", "top", "Right", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "onChange", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "color", "fontStyle", "textAlign", "padding", "backgroundColor", "borderRadius", "border", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/mathNode.js"], "sourcesContent": ["// mathNode.js\n// Demonstrates mathematical operations with multiple inputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { CustomDropdown } from '../components/CustomDropdown';\n\nexport const MathNode = ({ id, data }) => {\n  const [operation, setOperation] = useState(data?.operation || 'add');\n\n  const handleOperationChange = (e) => {\n    setOperation(e.target.value);\n  };\n\n  const handles = [\n    createHandle(`${id}-input1`, 'target', Position.Left, { top: '25%' }),\n    createHandle(`${id}-input2`, 'target', Position.Left, { top: '75%' }),\n    createHandle(`${id}-result`, 'source', Position.Right)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Math\"\n      handles={handles}\n      nodeType=\"math\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        <label style={commonLabelStyle}>\n          Operation:\n          <select\n            value={operation}\n            onChange={handleOperationChange}\n            className=\"node-input\"\n          >\n            <option value=\"add\">Add</option>\n            <option value=\"subtract\">Subtract</option>\n            <option value=\"multiply\">Multiply</option>\n            <option value=\"divide\">Divide</option>\n          </select>\n        </label>\n        <div style={{\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center',\n          padding: '4px 8px',\n          backgroundColor: 'rgba(138, 43, 226, 0.1)',\n          borderRadius: '4px',\n          border: '1px solid rgba(138, 43, 226, 0.2)'\n        }}>\n          Performs {operation} operation\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AACrE,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,cAAc,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,KAAK,CAAC;EAEpE,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,OAAO,GAAG,CACdhB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACe,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrElB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACe,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrElB,YAAY,CAAE,GAAEO,EAAG,SAAQ,EAAE,QAAQ,EAAEL,QAAQ,CAACiB,KAAK,CAAC,CACvD;EAED,oBACEd,OAAA,CAACN,QAAQ;IACPQ,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXY,KAAK,EAAC,MAAM;IACZJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,MAAM;IAAAC,QAAA,eAEfjB,OAAA;MAAKkB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnEjB,OAAA;QAAOkB,KAAK,EAAEtB,gBAAiB;QAAAqB,QAAA,GAAC,YAE9B,eAAAjB,OAAA;UACEU,KAAK,EAAEL,SAAU;UACjBiB,QAAQ,EAAEf,qBAAsB;UAChCgB,SAAS,EAAC,YAAY;UAAAN,QAAA,gBAEtBjB,OAAA;YAAQU,KAAK,EAAC,KAAK;YAAAO,QAAA,EAAC;UAAG;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChC3B,OAAA;YAAQU,KAAK,EAAC,UAAU;YAAAO,QAAA,EAAC;UAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1C3B,OAAA;YAAQU,KAAK,EAAC,UAAU;YAAAO,QAAA,EAAC;UAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1C3B,OAAA;YAAQU,KAAK,EAAC,QAAQ;YAAAO,QAAA,EAAC;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACR3B,OAAA;QAAKkB,KAAK,EAAE;UACVU,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,0BAA0B;UACjCC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,QAAQ;UACnBC,OAAO,EAAE,SAAS;UAClBC,eAAe,EAAE,yBAAyB;UAC1CC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAlB,QAAA,GAAC,WACQ,EAACZ,SAAS,EAAC,YACtB;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACvB,EAAA,CAlDWH,QAAQ;AAAAmC,EAAA,GAARnC,QAAQ;AAAA,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}