{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\outputNode.js\",\n  _s = $RefreshSig$();\n// outputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';\nimport { NodeInput } from '../components/NodeInput';\nimport { CustomDropdown } from '../components/CustomDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const OutputNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [currName, setCurrName] = useState((data === null || data === void 0 ? void 0 : data.outputName) || id.replace('customOutput-', 'output_'));\n  const [outputType, setOutputType] = useState(data.outputType || 'Text');\n  const handleNameChange = e => {\n    setCurrName(e.target.value);\n  };\n  const handleTypeChange = e => {\n    setOutputType(e.target.value);\n  };\n  const typeOptions = [{\n    value: 'Text',\n    label: 'Text'\n  }, {\n    value: 'File',\n    label: 'Image'\n  }];\n  const handles = [HANDLE_CONFIGS.targetLeft(`${id}-value`)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Output\",\n    handles: handles,\n    nodeType: \"output\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Name:\", /*#__PURE__*/_jsxDEV(NodeInput, {\n          value: currName,\n          onChange: handleNameChange,\n          placeholder: \"Enter output name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Type:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: outputType,\n          onChange: handleTypeChange,\n          className: \"node-input\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Text\",\n            children: \"Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"File\",\n            children: \"Image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(OutputNode, \"B7OtEwNf7ml8MnkX7q6OcbfpAhQ=\");\n_c = OutputNode;\nvar _c;\n$RefreshReg$(_c, \"OutputNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "HANDLE_CONFIGS", "commonLabelStyle", "NodeInput", "CustomDropdown", "jsxDEV", "_jsxDEV", "OutputNode", "id", "data", "_s", "currName", "setCurrName", "outputName", "replace", "outputType", "setOutputType", "handleNameChange", "e", "target", "value", "handleTypeChange", "typeOptions", "label", "handles", "targetLeft", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "onChange", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "cursor", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/outputNode.js"], "sourcesContent": ["// outputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';\nimport { NodeInput } from '../components/NodeInput';\nimport { CustomDropdown } from '../components/CustomDropdown';\n\nexport const OutputNode = ({ id, data }) => {\n  const [currName, setCurrName] = useState(data?.outputName || id.replace('customOutput-', 'output_'));\n  const [outputType, setOutputType] = useState(data.outputType || 'Text');\n\n  const handleNameChange = (e) => {\n    setCurrName(e.target.value);\n  };\n\n  const handleTypeChange = (e) => {\n    setOutputType(e.target.value);\n  };\n\n  const typeOptions = [\n    { value: 'Text', label: 'Text' },\n    { value: 'File', label: 'Image' }\n  ];\n\n  const handles = [HANDLE_CONFIGS.targetLeft(`${id}-value`)];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Output\"\n      handles={handles}\n      nodeType=\"output\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        <label style={commonLabelStyle}>\n          Name:\n          <NodeInput\n            value={currName}\n            onChange={handleNameChange}\n            placeholder=\"Enter output name\"\n          />\n        </label>\n        <label style={commonLabelStyle}>\n          Type:\n          <select\n            value={outputType}\n            onChange={handleTypeChange}\n            className=\"node-input\"\n            style={{ cursor: 'pointer' }}\n          >\n            <option value=\"Text\">Text</option>\n            <option value=\"File\">Image</option>\n          </select>\n        </label>\n      </div>\n    </BaseNode>\n  );\n}\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,YAAY;AACvE,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,cAAc,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMC,UAAU,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,UAAU,KAAIL,EAAE,CAACM,OAAO,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;EACpG,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAACU,IAAI,CAACM,UAAU,IAAI,MAAM,CAAC;EAEvE,MAAME,gBAAgB,GAAIC,CAAC,IAAK;IAC9BN,WAAW,CAACM,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9BF,aAAa,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAME,WAAW,GAAG,CAClB;IAAEF,KAAK,EAAE,MAAM;IAAEG,KAAK,EAAE;EAAO,CAAC,EAChC;IAAEH,KAAK,EAAE,MAAM;IAAEG,KAAK,EAAE;EAAQ,CAAC,CAClC;EAED,MAAMC,OAAO,GAAG,CAACvB,cAAc,CAACwB,UAAU,CAAE,GAAEjB,EAAG,QAAO,CAAC,CAAC;EAE1D,oBACEF,OAAA,CAACN,QAAQ;IACPQ,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXiB,KAAK,EAAC,QAAQ;IACdF,OAAO,EAAEA,OAAQ;IACjBG,QAAQ,EAAC,QAAQ;IAAAC,QAAA,eAEjBtB,OAAA;MAAKuB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnEtB,OAAA;QAAOuB,KAAK,EAAE3B,gBAAiB;QAAA0B,QAAA,GAAC,OAE9B,eAAAtB,OAAA,CAACH,SAAS;UACRiB,KAAK,EAAET,QAAS;UAChBsB,QAAQ,EAAEhB,gBAAiB;UAC3BiB,WAAW,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRhC,OAAA;QAAOuB,KAAK,EAAE3B,gBAAiB;QAAA0B,QAAA,GAAC,OAE9B,eAAAtB,OAAA;UACEc,KAAK,EAAEL,UAAW;UAClBkB,QAAQ,EAAEZ,gBAAiB;UAC3BkB,SAAS,EAAC,YAAY;UACtBV,KAAK,EAAE;YAAEW,MAAM,EAAE;UAAU,CAAE;UAAAZ,QAAA,gBAE7BtB,OAAA;YAAQc,KAAK,EAAC,MAAM;YAAAQ,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClChC,OAAA;YAAQc,KAAK,EAAC,MAAM;YAAAQ,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAA5B,EAAA,CAnDYH,UAAU;AAAAkC,EAAA,GAAVlC,UAAU;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}