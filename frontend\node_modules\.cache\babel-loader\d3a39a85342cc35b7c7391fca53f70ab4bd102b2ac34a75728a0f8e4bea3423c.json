{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\aggregatorNode.js\",\n  _s = $RefreshSig$();\n// aggregatorNode.js\n// Demonstrates data aggregation with multiple inputs and operations\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { NodeInput } from '../components/NodeInput';\nimport { CustomDropdown } from '../components/CustomDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AggregatorNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [operation, setOperation] = useState((data === null || data === void 0 ? void 0 : data.operation) || 'concat');\n  const [separator, setSeparator] = useState((data === null || data === void 0 ? void 0 : data.separator) || ', ');\n  const handleOperationChange = e => {\n    setOperation(e.target.value);\n  };\n  const handleSeparatorChange = e => {\n    setSeparator(e.target.value);\n  };\n  const handles = [createHandle(`${id}-input1`, 'target', Position.Left, {\n    top: '20%'\n  }), createHandle(`${id}-input2`, 'target', Position.Left, {\n    top: '40%'\n  }), createHandle(`${id}-input3`, 'target', Position.Left, {\n    top: '60%'\n  }), createHandle(`${id}-input4`, 'target', Position.Left, {\n    top: '80%'\n  }), createHandle(`${id}-result`, 'source', Position.Right)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Aggregator\",\n    handles: handles,\n    nodeType: \"aggregator\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Operation:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: operation,\n          onChange: handleOperationChange,\n          className: \"node-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"concat\",\n            children: \"Concat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"sum\",\n            children: \"Sum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"avg\",\n            children: \"Average\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"max\",\n            children: \"Max\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"min\",\n            children: \"Min\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), operation === 'concat' && /*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Separator:\", /*#__PURE__*/_jsxDEV(NodeInput, {\n          value: separator,\n          onChange: handleSeparatorChange,\n          placeholder: \"Enter separator\",\n          style: {\n            fontSize: '11px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center',\n          padding: '4px 8px',\n          backgroundColor: 'rgba(138, 43, 226, 0.1)',\n          borderRadius: '4px',\n          border: '1px solid rgba(138, 43, 226, 0.2)'\n        },\n        children: \"Combines multiple inputs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(AggregatorNode, \"sMY8CXrz1Xg3U6xCkKPfMgyFeGs=\");\n_c = AggregatorNode;\nvar _c;\n$RefreshReg$(_c, \"AggregatorNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "commonLabelStyle", "Position", "NodeInput", "CustomDropdown", "jsxDEV", "_jsxDEV", "AggregatorNode", "id", "data", "_s", "operation", "setOperation", "separator", "setSeparator", "handleOperationChange", "e", "target", "value", "handleSeparatorChange", "handles", "Left", "top", "Right", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "onChange", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "fontSize", "color", "fontStyle", "textAlign", "padding", "backgroundColor", "borderRadius", "border", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/aggregatorNode.js"], "sourcesContent": ["// aggregatorNode.js\n// Demonstrates data aggregation with multiple inputs and operations\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle, commonLabelStyle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { NodeInput } from '../components/NodeInput';\nimport { CustomDropdown } from '../components/CustomDropdown';\n\nexport const AggregatorNode = ({ id, data }) => {\n  const [operation, setOperation] = useState(data?.operation || 'concat');\n  const [separator, setSeparator] = useState(data?.separator || ', ');\n\n  const handleOperationChange = (e) => {\n    setOperation(e.target.value);\n  };\n\n  const handleSeparatorChange = (e) => {\n    setSeparator(e.target.value);\n  };\n\n  const handles = [\n    createHandle(`${id}-input1`, 'target', Position.Left, { top: '20%' }),\n    createHandle(`${id}-input2`, 'target', Position.Left, { top: '40%' }),\n    createHandle(`${id}-input3`, 'target', Position.Left, { top: '60%' }),\n    createHandle(`${id}-input4`, 'target', Position.Left, { top: '80%' }),\n    createHandle(`${id}-result`, 'source', Position.Right)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Aggregator\"\n      handles={handles}\n      nodeType=\"aggregator\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        <label style={commonLabelStyle}>\n          Operation:\n          <select\n            value={operation}\n            onChange={handleOperationChange}\n            className=\"node-input\"\n          >\n            <option value=\"concat\">Concat</option>\n            <option value=\"sum\">Sum</option>\n            <option value=\"avg\">Average</option>\n            <option value=\"max\">Max</option>\n            <option value=\"min\">Min</option>\n          </select>\n        </label>\n        {operation === 'concat' && (\n          <label style={commonLabelStyle}>\n            Separator:\n            <NodeInput\n              value={separator}\n              onChange={handleSeparatorChange}\n              placeholder=\"Enter separator\"\n              style={{ fontSize: '11px' }}\n            />\n          </label>\n        )}\n        <div style={{\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center',\n          padding: '4px 8px',\n          backgroundColor: 'rgba(138, 43, 226, 0.1)',\n          borderRadius: '4px',\n          border: '1px solid rgba(138, 43, 226, 0.2)'\n        }}>\n          Combines multiple inputs\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AACrE,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,cAAc,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,CAAAW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,QAAQ,CAAC;EACvE,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,CAAAW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,SAAS,KAAI,IAAI,CAAC;EAEnE,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,qBAAqB,GAAIH,CAAC,IAAK;IACnCF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAME,OAAO,GAAG,CACdpB,YAAY,CAAE,GAAEQ,EAAG,SAAQ,EAAE,QAAQ,EAAEN,QAAQ,CAACmB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrEtB,YAAY,CAAE,GAAEQ,EAAG,SAAQ,EAAE,QAAQ,EAAEN,QAAQ,CAACmB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrEtB,YAAY,CAAE,GAAEQ,EAAG,SAAQ,EAAE,QAAQ,EAAEN,QAAQ,CAACmB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrEtB,YAAY,CAAE,GAAEQ,EAAG,SAAQ,EAAE,QAAQ,EAAEN,QAAQ,CAACmB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACrEtB,YAAY,CAAE,GAAEQ,EAAG,SAAQ,EAAE,QAAQ,EAAEN,QAAQ,CAACqB,KAAK,CAAC,CACvD;EAED,oBACEjB,OAAA,CAACP,QAAQ;IACPS,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXe,KAAK,EAAC,YAAY;IAClBJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,YAAY;IAAAC,QAAA,eAErBpB,OAAA;MAAKqB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnEpB,OAAA;QAAOqB,KAAK,EAAE1B,gBAAiB;QAAAyB,QAAA,GAAC,YAE9B,eAAApB,OAAA;UACEY,KAAK,EAAEP,SAAU;UACjBoB,QAAQ,EAAEhB,qBAAsB;UAChCiB,SAAS,EAAC,YAAY;UAAAN,QAAA,gBAEtBpB,OAAA;YAAQY,KAAK,EAAC,QAAQ;YAAAQ,QAAA,EAAC;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC9B,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAQ,QAAA,EAAC;UAAG;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChC9B,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAQ,QAAA,EAAC;UAAO;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpC9B,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAQ,QAAA,EAAC;UAAG;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChC9B,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAQ,QAAA,EAAC;UAAG;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EACPzB,SAAS,KAAK,QAAQ,iBACrBL,OAAA;QAAOqB,KAAK,EAAE1B,gBAAiB;QAAAyB,QAAA,GAAC,YAE9B,eAAApB,OAAA,CAACH,SAAS;UACRe,KAAK,EAAEL,SAAU;UACjBkB,QAAQ,EAAEZ,qBAAsB;UAChCkB,WAAW,EAAC,iBAAiB;UAC7BV,KAAK,EAAE;YAAEW,QAAQ,EAAE;UAAO;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACR,eACD9B,OAAA;QAAKqB,KAAK,EAAE;UACVW,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,0BAA0B;UACjCC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,QAAQ;UACnBC,OAAO,EAAE,SAAS;UAClBC,eAAe,EAAE,yBAAyB;UAC1CC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,EAAC;MAEH;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAC1B,EAAA,CArEWH,cAAc;AAAAuC,EAAA,GAAdvC,cAAc;AAAA,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}