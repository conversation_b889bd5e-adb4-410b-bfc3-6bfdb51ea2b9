{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\switchNode.js\",\n  _s = $RefreshSig$();\n// switchNode.js\n// Demonstrates conditional routing with multiple outputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { CustomDropdown } from '../components/CustomDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const SwitchNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [condition, setCondition] = useState((data === null || data === void 0 ? void 0 : data.condition) || 'true');\n  const handleConditionChange = e => {\n    setCondition(e.target.value);\n  };\n  const conditionOptions = [{\n    value: 'true',\n    label: 'True'\n  }, {\n    value: 'false',\n    label: 'False'\n  }];\n  const handles = [createHandle(`${id}-input`, 'target', Position.Left), createHandle(`${id}-condition`, 'target', Position.Top), createHandle(`${id}-true`, 'source', Position.Right, {\n    top: '30%'\n  }), createHandle(`${id}-false`, 'source', Position.Right, {\n    top: '70%'\n  })];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Switch\",\n    handles: handles,\n    nodeType: \"switch\",\n    height: 90,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"inline-label\",\n        children: [\"Default:\", /*#__PURE__*/_jsxDEV(CustomDropdown, {\n          value: condition,\n          onChange: handleConditionChange,\n          options: conditionOptions,\n          className: \"node-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)'\n        },\n        children: \"Routes based on condition\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(SwitchNode, \"8qbhjYQ3rNaRhZI97gO2kO7T3ms=\");\n_c = SwitchNode;\nvar _c;\n$RefreshReg$(_c, \"SwitchNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "Position", "CustomDropdown", "jsxDEV", "_jsxDEV", "SwitchNode", "id", "data", "_s", "condition", "setCondition", "handleConditionChange", "e", "target", "value", "conditionOptions", "label", "handles", "Left", "Top", "Right", "top", "title", "nodeType", "height", "children", "className", "onChange", "options", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "color", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/switchNode.js"], "sourcesContent": ["// switchNode.js\n// Demonstrates conditional routing with multiple outputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { CustomDropdown } from '../components/CustomDropdown';\n\nexport const SwitchNode = ({ id, data }) => {\n  const [condition, setCondition] = useState(data?.condition || 'true');\n\n  const handleConditionChange = (e) => {\n    setCondition(e.target.value);\n  };\n\n  const conditionOptions = [\n    { value: 'true', label: 'True' },\n    { value: 'false', label: 'False' }\n  ];\n\n  const handles = [\n    createHandle(`${id}-input`, 'target', Position.Left),\n    createHandle(`${id}-condition`, 'target', Position.Top),\n    createHandle(`${id}-true`, 'source', Position.Right, { top: '30%' }),\n    createHandle(`${id}-false`, 'source', Position.Right, { top: '70%' })\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Switch\"\n      handles={handles}\n      nodeType=\"switch\"\n      height={90}\n    >\n      <div>\n        <label className=\"inline-label\">\n          Default:\n          <CustomDropdown\n            value={condition}\n            onChange={handleConditionChange}\n            options={conditionOptions}\n            className=\"node-input\"\n          />\n        </label>\n        <div style={{ fontSize: '10px', color: 'rgba(255, 255, 255, 0.6)' }}>\n          Routes based on condition\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,YAAY;AACnD,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,cAAc,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMC,UAAU,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,MAAM,CAAC;EAErE,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,gBAAgB,GAAG,CACvB;IAAED,KAAK,EAAE,MAAM;IAAEE,KAAK,EAAE;EAAO,CAAC,EAChC;IAAEF,KAAK,EAAE,OAAO;IAAEE,KAAK,EAAE;EAAQ,CAAC,CACnC;EAED,MAAMC,OAAO,GAAG,CACdjB,YAAY,CAAE,GAAEM,EAAG,QAAO,EAAE,QAAQ,EAAEL,QAAQ,CAACiB,IAAI,CAAC,EACpDlB,YAAY,CAAE,GAAEM,EAAG,YAAW,EAAE,QAAQ,EAAEL,QAAQ,CAACkB,GAAG,CAAC,EACvDnB,YAAY,CAAE,GAAEM,EAAG,OAAM,EAAE,QAAQ,EAAEL,QAAQ,CAACmB,KAAK,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACpErB,YAAY,CAAE,GAAEM,EAAG,QAAO,EAAE,QAAQ,EAAEL,QAAQ,CAACmB,KAAK,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,CACtE;EAED,oBACEjB,OAAA,CAACL,QAAQ;IACPO,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXe,KAAK,EAAC,QAAQ;IACdL,OAAO,EAAEA,OAAQ;IACjBM,QAAQ,EAAC,QAAQ;IACjBC,MAAM,EAAE,EAAG;IAAAC,QAAA,eAEXrB,OAAA;MAAAqB,QAAA,gBACErB,OAAA;QAAOsB,SAAS,EAAC,cAAc;QAAAD,QAAA,GAAC,UAE9B,eAAArB,OAAA,CAACF,cAAc;UACbY,KAAK,EAAEL,SAAU;UACjBkB,QAAQ,EAAEhB,qBAAsB;UAChCiB,OAAO,EAAEb,gBAAiB;UAC1BW,SAAS,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACR5B,OAAA;QAAK6B,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAA2B,CAAE;QAAAV,QAAA,EAAC;MAErE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACxB,EAAA,CA5CWH,UAAU;AAAA+B,EAAA,GAAV/B,UAAU;AAAA,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}