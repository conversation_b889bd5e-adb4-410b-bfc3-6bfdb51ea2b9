/* Import VectorShift Fonts - with fallback */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');

/* Fallback fonts in case Google Fonts fails */
@font-face {
  font-family: 'Studio Feixen Sans Fallback';
  src: local('Arial'), local('Helvetica'), local('sans-serif');
  font-weight: normal;
  font-style: normal;
}

/* Studio Feixen Sans Font Family - Actual Font Files */
@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Ultralight.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Book.ttf') format('truetype');
  font-weight: 450;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Semibold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* CSS Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  font-family: 'Studio Feixen Sans', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  color: #ffffff;
}

code {
  font-family: 'JetBrains Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* App Layout - Performance Optimized */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  color: #ffffff;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  position: relative;
}

/* Header Section - Clean Text Only */
.pipeline-header {
  padding: 20px 24px 12px 24px;
  margin: 0 16px;
}

/* Node Toolbar Section - Professional Pipeline Design */
.pipeline-node-toolbar {
  background: rgba(26, 11, 46, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(138, 43, 226, 0.4);
  border-radius: 16px;
  padding: 16px 24px;
  box-shadow:
    0 8px 32px rgba(138, 43, 226, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  margin: 0 16px 20px 16px;
  position: relative;
  overflow: hidden;
}

/* Header Content - Clean Layout */
.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  flex-wrap: nowrap;
  min-height: auto;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

/* Node Toolbar Content - Professional Layout */
.node-toolbar-content {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: nowrap;
  min-height: 48px;
  position: relative;
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-svg {
  filter: drop-shadow(0 0 8px rgba(138, 43, 226, 0.5));
  transition: all 0.3s ease;
}

.logo-svg:hover {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 0 12px rgba(138, 43, 226, 0.8));
}

.brand-text {
  flex: 1;
}

.header-title {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  background: linear-gradient(90deg, #ffffff 0%, #e2e8f0 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  white-space: nowrap;
  transition: font-size 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 400px;
}

/* Toolbar Label - Professional Styling */
.toolbar-label {
  font-size: 13px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 1px;
  white-space: nowrap;
  flex-shrink: 0;
  position: relative;
  padding-right: 20px;
}

.toolbar-label::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 20px;
  background: linear-gradient(to bottom, transparent, rgba(138, 43, 226, 0.6), transparent);
}

.toolbar-nodes {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: nowrap;
  flex: 1;
  justify-content: flex-start;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: 6px 0;
  min-width: 0;
  position: relative;
}

.toolbar-nodes::-webkit-scrollbar {
  display: none;
}

/* Draggable Node Styles - Professional Pipeline Components */
.draggable-node {
  cursor: grab;
  min-width: 100px;
  max-width: 120px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(26, 11, 46, 0.9) 0%, rgba(30, 15, 50, 0.9) 100%);
  border: 1px solid rgba(138, 43, 226, 0.6);
  color: #ffffff;
  font-weight: 600;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  backdrop-filter: blur(15px);
  box-shadow:
    0 4px 12px rgba(138, 43, 226, 0.25),
    0 2px 6px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  padding: 0 12px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

.draggable-node:hover {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%);
  border-color: rgba(138, 43, 226, 0.9);
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 8px 20px rgba(138, 43, 226, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.draggable-node:active {
  cursor: grabbing;
  transform: translateY(0) scale(0.95);
  box-shadow:
    0 2px 8px rgba(138, 43, 226, 0.3),
    inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Pipeline UI Container - VectorShift Style */
.pipeline-ui-container {
  flex: 1;
  position: relative;
  background: rgba(26, 11, 46, 0.4);
  margin: 8px 16px 16px 16px;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(138, 43, 226, 0.3);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(138, 43, 226, 0.15);
  width: calc(100% - 32px);
  height: calc(100vh - 200px);
  min-height: 500px;
}

/* Submit Button Container - VectorShift Style */
.submit-container {
  padding: 16px 24px;
  background: rgba(26, 11, 46, 0.95);
  border-top: 1px solid rgba(138, 43, 226, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  backdrop-filter: blur(20px);
  min-height: auto;
}

.submit-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.pipeline-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: rgba(255, 255, 255, 0.6);
}

.stat-divider {
  color: rgba(138, 43, 226, 0.5);
  font-size: 16px;
}

.getting-started {
  text-align: center;
}

.start-hint {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(138, 43, 226, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(138, 43, 226, 0.2);
}

.submit-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  color: #ffffff;
  border: 1px solid rgba(138, 43, 226, 0.4);
  padding: 10px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
  backdrop-filter: blur(10px);
}

.submit-button:hover {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
  border-color: rgba(138, 43, 226, 0.6);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.4);
}

.submit-button:active {
  transform: translateY(0) scale(0.98);
}

.submit-button.disabled {
  background: rgba(26, 11, 46, 0.6);
  border-color: rgba(138, 43, 226, 0.2);
  cursor: not-allowed;
  opacity: 0.6;
}

.submit-button.disabled:hover {
  transform: none;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.1);
  border-color: rgba(138, 43, 226, 0.2);
}

.submit-button:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* ReactFlow Custom Styling - VectorShift Theme */
.react-flow__controls {
  background: rgba(26, 11, 46, 0.95) !important;
  border: 1px solid rgba(138, 43, 226, 0.5) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 4px 16px rgba(138, 43, 226, 0.2) !important;
  bottom: 20px !important;
  left: 20px !important;
  padding: 2px !important;
}

.react-flow__controls-button {
  background: rgba(26, 11, 46, 0.8) !important;
  border: 1px solid rgba(138, 43, 226, 0.5) !important;
  color: #ffffff !important;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 6px !important;
  width: 24px !important;
  height: 24px !important;
  margin: 1px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.react-flow__controls-button:hover {
  background: rgba(26, 11, 46, 0.95) !important;
  border-color: rgba(138, 43, 226, 0.8) !important;
  color: #ffffff !important;
  transform: scale(1.05) !important;
  box-shadow: 0 2px 8px rgba(138, 43, 226, 0.4) !important;
}

.react-flow__controls-button svg {
  fill: currentColor !important;
  width: 14px !important;
  height: 14px !important;
}

.react-flow__minimap {
  background: rgba(26, 11, 46, 0.95) !important;
  border: 1px solid rgba(138, 43, 226, 0.5) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 4px 16px rgba(138, 43, 226, 0.2) !important;
  bottom: 20px !important;
  right: 20px !important;
  width: 120px !important;
  height: 80px !important;
}

.react-flow__edge-path {
  stroke: #8b5cf6 !important;
  stroke-width: 2px !important;
  filter: drop-shadow(0 0 4px rgba(139, 92, 246, 0.5)) !important;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #a855f7 !important;
  stroke-width: 3px !important;
  filter: drop-shadow(0 0 8px rgba(168, 85, 247, 0.8)) !important;
}

.react-flow__connection-line {
  stroke: #8b5cf6 !important;
  stroke-width: 2px !important;
  filter: drop-shadow(0 0 4px rgba(139, 92, 246, 0.5)) !important;
}

/* Custom scrollbar - VectorShift Style */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(26, 11, 46, 0.4);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
}

/* VectorShift Professional Animations */
@keyframes vectorShiftGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(138, 43, 226, 0.5);
  }
}

@keyframes vectorShiftPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes vectorShiftSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Apply animations to key elements */
.pipeline-toolbar {
  animation: vectorShiftSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.draggable-node {
  animation: vectorShiftSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.submit-button {
  animation: vectorShiftGlow 3s ease-in-out infinite;
}

/* Professional focus states */
.draggable-node:focus,
.submit-button:focus {
  outline: 2px solid rgba(138, 43, 226, 0.6);
  outline-offset: 2px;
}

/* Enhanced backdrop effects */
.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(168, 85, 247, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: -1;
  animation: vectorShiftBackdrop 20s ease-in-out infinite;
}

@keyframes vectorShiftBackdrop {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Professional node connection animations */
.react-flow__edge-path {
  animation: vectorShiftFlow 2s ease-in-out infinite;
}

@keyframes vectorShiftFlow {
  0%, 100% {
    stroke-dasharray: 5, 5;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dashoffset: 10;
  }
}

/* Toolbar section staggered animations */
.toolbar-section:nth-child(1) { animation-delay: 0.1s; }
.toolbar-section:nth-child(2) { animation-delay: 0.2s; }
.toolbar-section:nth-child(3) { animation-delay: 0.3s; }
.toolbar-section:nth-child(4) { animation-delay: 0.4s; }

/* Node hover glow effect */
.base-node {
  position: relative;
  overflow: visible;
}

.base-node::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.3), rgba(79, 70, 229, 0.3));
  border-radius: 18px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.base-node:hover::before {
  opacity: 1;
}

/* Professional drag state */
.react-flow__node.dragging {
  transform: rotate(2deg) scale(1.05);
  filter: drop-shadow(0 10px 20px rgba(138, 43, 226, 0.4));
  z-index: 1000;
}

/* Submit button enhanced states */
.submit-button {
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover::before {
  left: 100%;
}

/* VectorShift-style loading indicator */
@keyframes vectorShiftSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design for Mobile and Tablet */
@media (max-width: 768px) {
  /* Header responsive styles */
  .pipeline-header {
    padding: 16px 16px 8px 16px;
    margin: 0 12px;
  }

  /* Node toolbar responsive styles */
  .pipeline-node-toolbar {
    padding: 12px 16px;
    margin: 0 12px 16px 12px;
    border-radius: 12px;
  }

  .header-content {
    gap: 12px;
    justify-content: flex-start;
  }

  .header-brand {
    gap: 8px;
    flex-shrink: 0;
  }

  .header-title {
    font-size: 18px;
    max-width: 300px;
  }

  .node-toolbar-content {
    gap: 16px;
  }

  .toolbar-label {
    font-size: 12px;
  }

  .toolbar-nodes {
    gap: 8px;
  }

  .draggable-node {
    min-width: 85px;
    max-width: 100px;
    height: 36px;
    font-size: 10px;
  }

  .draggable-node {
    min-width: 70px;
    max-width: 90px;
    height: 32px;
    font-size: 9px;
    padding: 0 8px;
    border-radius: 6px;
  }

  /* Pipeline UI responsive adjustments */
  .pipeline-ui-container {
    margin: 8px;
    width: calc(100% - 16px);
    height: calc(100vh - 220px);
  }

  /* Submit container styles */
  .submit-container {
    padding: 12px 16px;
    gap: 8px;
  }

  .submit-button {
    padding: 8px 24px;
    font-size: 13px;
  }

  .pipeline-stats {
    gap: 8px;
    font-size: 12px;
  }

  .stat-number {
    font-size: 18px;
  }

  .stat-label {
    font-size: 11px;
  }

  .getting-started-text {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  /* Header responsive styles for small mobile */
  .pipeline-header {
    padding: 12px 12px 6px 12px;
    margin: 0 8px;
  }

  /* Node toolbar responsive styles for small mobile */
  .pipeline-node-toolbar {
    padding: 10px 12px;
    margin: 0 8px 12px 8px;
    border-radius: 10px;
  }

  .header-content {
    gap: 8px;
    justify-content: center;
  }

  .header-brand {
    gap: 6px;
    flex-shrink: 0;
  }

  .brand-logo .logo-svg {
    width: 24px;
    height: 24px;
  }

  .header-title {
    font-size: 14px;
    max-width: 200px;
  }

  .node-toolbar-content {
    gap: 12px;
  }

  .toolbar-label {
    font-size: 10px;
  }

  .toolbar-nodes {
    gap: 6px;
  }

  .draggable-node {
    min-width: 70px;
    max-width: 85px;
    height: 32px;
    font-size: 9px;
    padding: 0 8px;
  }

  .draggable-node {
    min-width: 60px;
    max-width: 80px;
    height: 28px;
    font-size: 8px;
    padding: 0 6px;
    border-radius: 6px;
  }

  /* Pipeline UI responsive adjustments for small mobile */
  .pipeline-ui-container {
    margin: 4px;
    width: calc(100% - 8px);
    height: calc(100vh - 240px);
    border-radius: 12px;
  }

  /* Submit container styles */
  .submit-container {
    padding: 10px 12px;
    gap: 6px;
  }

  .submit-button {
    padding: 8px 20px;
    font-size: 12px;
  }

  .pipeline-stats {
    flex-direction: column;
    gap: 4px;
  }

  .stat-divider {
    display: none;
  }

  .stat-number {
    font-size: 16px;
  }

  .stat-label {
    font-size: 10px;
  }
}

/* Extra small mobile screens */
@media (max-width: 360px) {
  .pipeline-header {
    padding: 10px 8px 4px 8px;
    margin: 0 6px;
  }

  .pipeline-node-toolbar {
    padding: 8px 10px;
    margin: 0 6px 10px 6px;
    border-radius: 8px;
  }

  .header-title {
    font-size: 12px;
    max-width: 120px;
  }

  .toolbar-label {
    font-size: 9px;
  }

  .toolbar-nodes {
    gap: 4px;
  }

  .draggable-node {
    min-width: 60px;
    max-width: 75px;
    height: 28px;
    font-size: 8px;
    padding: 0 6px;
  }

  .draggable-node {
    min-width: 45px;
    max-width: 65px;
    height: 24px;
    font-size: 7px;
    padding: 0 3px;
    border-radius: 5px;
  }
}

/* Responsive styles for medium tablets */
@media (min-width: 481px) and (max-width: 768px) {
  .pipeline-header {
    padding: 18px 20px 10px 20px;
    margin: 0 16px;
  }

  .pipeline-node-toolbar {
    padding: 14px 20px;
    margin: 0 16px 20px 16px;
    border-radius: 14px;
  }

  .header-content {
    gap: 12px;
    justify-content: flex-start;
  }

  .header-brand {
    flex-shrink: 0;
  }

  .header-title {
    font-size: 20px;
    max-width: 280px;
  }

  .node-toolbar-content {
    gap: 18px;
  }

  .toolbar-label {
    font-size: 13px;
  }

  .toolbar-nodes {
    gap: 10px;
  }

  .draggable-node {
    min-width: 90px;
    max-width: 110px;
    height: 38px;
    font-size: 10px;
  }

  .toolbar-nodes::-webkit-scrollbar {
    display: none;
  }

  .draggable-node {
    min-width: 80px;
    max-width: 100px;
    height: 34px;
    font-size: 9px;
    padding: 0 8px;
    border-radius: 7px;
  }
}

/* Responsive styles for larger screens */
@media (min-width: 1200px) {
  .pipeline-header {
    padding: 24px 32px 16px 32px;
    margin: 0 24px;
  }

  .pipeline-node-toolbar {
    padding: 20px 32px;
    margin: 0 24px 24px 24px;
    border-radius: 20px;
  }

  .header-title {
    font-size: 24px;
  }

  .toolbar-label {
    font-size: 15px;
  }

  .toolbar-nodes {
    gap: 16px;
  }

  .draggable-node {
    min-width: 110px;
    max-width: 130px;
    height: 44px;
    font-size: 12px;
  }

  .draggable-node {
    min-width: 100px;
    max-width: 120px;
    padding: 0 12px;
    font-size: 11px;
    border-radius: 10px;
  }
}

/* Responsive styles for ultra-wide screens */
@media (min-width: 1600px) {
  .pipeline-header {
    padding: 28px 40px 20px 40px;
    margin: 0 32px;
  }

  .pipeline-node-toolbar {
    padding: 24px 40px;
    margin: 0 32px 32px 32px;
    border-radius: 24px;
  }

  .header-title {
    font-size: 26px;
  }

  .toolbar-label {
    font-size: 16px;
  }

  .toolbar-nodes {
    gap: 20px;
  }

  .draggable-node {
    min-width: 120px;
    max-width: 140px;
    height: 48px;
    font-size: 13px;
  }

  .draggable-node {
    min-width: 110px;
    max-width: 130px;
    padding: 0 14px;
    font-size: 12px;
    border-radius: 12px;
  }
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(138, 43, 226, 0.3);
  border-top: 2px solid #8b5cf6;
  border-radius: 50%;
  animation: vectorShiftSpin 1s linear infinite;
}

/* Professional focus indicators */
.react-flow__controls-button:focus {
  outline: 2px solid rgba(138, 43, 226, 0.6);
  outline-offset: 2px;
}

/* Enhanced minimap styling */
.react-flow__minimap-mask {
  fill: rgba(138, 43, 226, 0.1) !important;
}

.react-flow__minimap-node {
  fill: rgba(138, 43, 226, 0.6) !important;
  stroke: rgba(138, 43, 226, 0.8) !important;
  stroke-width: 1px !important;
}

/* Viewport indicator (the rectangle showing current view) */
.react-flow__minimap-viewport {
  fill: none !important;
  stroke: rgba(168, 85, 247, 0.8) !important;
  stroke-width: 2px !important;
  stroke-dasharray: none !important;
}

/* Ensure proper scaling within the smaller minimap */
.react-flow__minimap svg {
  width: 100% !important;
  height: 100% !important;
}

/* Fix viewport mask positioning and scaling */
.react-flow__minimap-mask {
  fill: rgba(15, 12, 26, 0.7) !important;
  opacity: 0.8 !important;
}

/* Node Input Field Styling - VectorShift Professional */
.node-input {
  min-width: 100px;
  max-width: 100%;
  white-space: nowrap;
  overflow-x: auto;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: rgba(26, 11, 46, 0.8);
  color: #ffffff;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid rgba(138, 43, 226, 0.5);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 2px 8px rgba(138, 43, 226, 0.1);
  height: 32px;
  box-sizing: border-box;
}

.node-input:focus {
  border-color: rgba(138, 43, 226, 0.8);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2), 0 4px 20px rgba(138, 43, 226, 0.3);
  background-color: rgba(26, 11, 46, 0.95);
  transform: scale(1.02);
  animation: dropdownGlow 2s ease-in-out infinite;
}

.node-input:hover {
  border-color: rgba(138, 43, 226, 0.6);
  background-color: rgba(26, 11, 46, 0.9);
  transform: scale(1.01);
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.2);
}

.node-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Input container with tooltip */
.input-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.input-tooltip {
  position: absolute;
  bottom: 100%;
  left: 0;
  background: rgba(26, 11, 46, 0.95);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 200ms ease;
  border: 1px solid rgba(138, 43, 226, 0.4);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  margin-bottom: 4px;
}

.input-container:hover .input-tooltip {
  opacity: 1;
  visibility: visible;
}

/* Modal Styling - VectorShift Professional */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: modalBackdropFadeIn 0.3s ease;
}

@keyframes modalBackdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-container {
  background: rgba(26, 11, 46, 0.95);
  border-radius: 12px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(138, 43, 226, 0.5);
  backdrop-filter: blur(20px);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(138, 43, 226, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(138, 43, 226, 0.3);
}

.modal-title {
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #f5f5f5;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 200ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.modal-close:hover {
  background: rgba(138, 43, 226, 0.2);
  color: #ffffff;
}

.modal-content {
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 16px;
  color: #f5f5f5;
  line-height: 1.6;
}

.modal-section {
  margin-bottom: 16px;
}

.modal-section:last-child {
  margin-bottom: 0;
}

.modal-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(138, 43, 226, 0.1);
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid rgba(138, 43, 226, 0.2);
}

.modal-stat-icon {
  font-size: 18px;
}

.modal-stat-text {
  font-weight: 500;
}

.modal-status {
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-status.success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

.modal-status.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid rgba(138, 43, 226, 0.3);
}

.modal-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 200ms ease;
  border: none;
}

.modal-button.primary {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  color: white;
}

.modal-button.primary:hover {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
  transform: translateY(-1px);
}

.modal-button.secondary {
  background: rgba(138, 43, 226, 0.1);
  color: #a855f7;
  border: 1px solid rgba(138, 43, 226, 0.3);
}

.modal-button.secondary:hover {
  background: rgba(138, 43, 226, 0.2);
}

/* Textarea styling to match node inputs */
textarea.node-textarea {
  min-width: 100%;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: #1b1b2b;
  color: white;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  border: 1px solid rgba(138, 43, 226, 0.3);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  resize: none;
}

textarea.node-textarea:focus {
  border-color: rgba(138, 43, 226, 0.8);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2);
  background-color: #1e1e2f;
}

textarea.node-textarea:hover {
  border-color: rgba(138, 43, 226, 0.6);
  background-color: #1e1e2f;
}

textarea.node-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Node Label Styling for Better Visibility */
.base-node label {
  color: #ffffff;
  font-weight: 600;
  font-size: 11px;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

/* Standardize all node content containers */
.base-node > div {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

/* Fix inline label styling */
.base-node label.inline-label {
  flex-direction: row;
  align-items: center;
  gap: 6px;
  font-size: 11px;
}

.base-node label.inline-label input,
.base-node label.inline-label select {
  margin-left: 0 !important;
  flex: 1;
  min-width: 60px;
}

/* Ensure consistent spacing for description text */
.base-node > div > div:last-child {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
}

/* Fix BaseNode content alignment */
.base-node {
  justify-content: flex-start;
  align-items: stretch;
  box-sizing: border-box;
  position: relative;
  overflow: visible;
}

.base-node > div:first-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
}

/* Node title styling */
.base-node h3 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 700;
  color: #ffffff;
  text-align: center;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
}

/* Ensure consistent content wrapper */
.base-node .node-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.base-node select {
  min-width: 100px;
  max-width: 100%;
  width: 100%;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: rgba(26, 11, 46, 0.8);
  color: #ffffff;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid rgba(138, 43, 226, 0.5);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  height: 32px;
  box-sizing: border-box;
  position: relative;
  backdrop-filter: blur(10px);
}

/* Add a subtle glow effect on select */
.base-node select::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, rgba(138, 43, 226, 0.1), rgba(99, 102, 241, 0.1));
  border-radius: 8px;
  z-index: -1;
  opacity: 0;
  transition: opacity 300ms ease;
}

.base-node select:focus {
  border-color: rgba(138, 43, 226, 0.8);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2), 0 4px 20px rgba(138, 43, 226, 0.3);
  background-color: rgba(26, 11, 46, 0.9);
  z-index: 10;
}

.base-node select:hover {
  border-color: rgba(138, 43, 226, 0.7);
  background-color: rgba(26, 11, 46, 0.9);
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.2);
}

.base-node select option {
  background-color: rgba(26, 11, 46, 0.95);
  color: #ffffff;
  padding: 8px;
  transition: all 200ms ease-out;
  border-radius: 4px;
  margin: 1px 0;
  backdrop-filter: blur(10px);
  border: 1px solid transparent;
}

.base-node select option:hover {
  background-color: rgba(138, 43, 226, 0.4);
  color: #ffffff;
  border-color: rgba(138, 43, 226, 0.6);
  box-shadow: 0 2px 8px rgba(138, 43, 226, 0.3);
}

.base-node select option:checked {
  background-color: rgba(138, 43, 226, 0.7);
  color: #ffffff;
  font-weight: 600;
  border-color: rgba(138, 43, 226, 0.8);
  box-shadow: 0 0 0 1px rgba(138, 43, 226, 0.5);
}

/* Smooth dropdown opening animation */
@keyframes smoothDropdownOpen {
  0% {
    opacity: 0;
    transform: translateY(-5px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes dropdownGlow {
  0%, 100% {
    box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2), 0 4px 20px rgba(138, 43, 226, 0.3);
  }
  50% {
    box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.3), 0 4px 22px rgba(138, 43, 226, 0.35);
  }
}

@keyframes selectPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.003);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes optionFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-3px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Apply smooth dropdown animations */
.base-node select:focus {
  animation: smoothDropdownOpen 0.2s ease-out;
}

.base-node select:hover {
  animation: none;
}

.base-node select:active {
  animation: none;
}

/* Enhanced dropdown container animation */
.base-node label {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.base-node label:has(select:focus) {
  transform: none;
}

.base-node label:has(select:hover) {
  transform: none;
}

/* Custom Dropdown Styles */
.custom-dropdown {
  position: relative;
  width: 100%;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 100px;
  width: 100%;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: rgba(26, 11, 46, 0.8);
  color: #ffffff;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid rgba(138, 43, 226, 0.5);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  height: 32px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  user-select: none;
}

.dropdown-trigger:hover {
  border-color: rgba(138, 43, 226, 0.7);
  background-color: rgba(26, 11, 46, 0.9);
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.2);
}

.dropdown-trigger:focus,
.custom-dropdown.open .dropdown-trigger {
  border-color: rgba(138, 43, 226, 0.8);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2), 0 4px 20px rgba(138, 43, 226, 0.3);
  background-color: rgba(26, 11, 46, 0.9);
}

.dropdown-text {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  color: rgba(255, 255, 255, 0.7);
  flex-shrink: 0;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
  color: rgba(138, 43, 226, 0.9);
}

.dropdown-arrow svg {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(26, 11, 46, 0.95);
  border: 1px solid rgba(138, 43, 226, 0.6);
  border-radius: 6px;
  margin-top: 4px;
  backdrop-filter: blur(15px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(138, 43, 226, 0.2);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px) scale(0.95);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-options.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
  animation: dropdownSlideIn 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-option {
  padding: 8px 12px;
  font-size: 11px;
  font-weight: 500;
  color: #ffffff;
  cursor: pointer;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 4px;
  margin: 2px 4px;
  position: relative;
  overflow: hidden;
}

.dropdown-option:hover,
.dropdown-option.focused {
  background-color: rgba(138, 43, 226, 0.3);
  color: #ffffff;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(138, 43, 226, 0.2);
}

.dropdown-option.selected {
  background-color: rgba(138, 43, 226, 0.5);
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 0 0 1px rgba(138, 43, 226, 0.4);
}

.dropdown-option.selected:hover,
.dropdown-option.selected.focused {
  background-color: rgba(138, 43, 226, 0.6);
  transform: translateX(2px);
}

.custom-dropdown.disabled .dropdown-trigger {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Dropdown animations */
@keyframes dropdownSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced ReactFlow Background Dots */
.react-flow__background {
  background-color: transparent !important;
}

.react-flow__background svg {
  opacity: 1 !important;
}

.react-flow__background circle {
  fill: #8b5cf6 !important;
  opacity: 0.6 !important;
}
