// filterNode.js
// Demonstrates data filtering with configurable conditions

import { useState } from 'react';
import { BaseNode, createHandle, commonLabelStyle } from './BaseNode';
import { Position } from 'reactflow';
import { CustomDropdown } from '../components/CustomDropdown';

export const FilterNode = ({ id, data }) => {
  const [condition, setCondition] = useState(data?.condition || 'contains');
  const [value, setValue] = useState(data?.value || '');

  const handleConditionChange = (e) => {
    setCondition(e.target.value);
  };

  const handleValueChange = (e) => {
    setValue(e.target.value);
  };

  const conditionOptions = [
    { value: 'contains', label: 'Contains' },
    { value: 'equals', label: 'Equals' },
    { value: 'startsWith', label: 'Starts with' }
  ];

  const handles = [
    createHandle(`${id}-input`, 'target', Position.Left),
    createHandle(`${id}-filtered`, 'source', Position.Right, { top: '30%' }),
    createHandle(`${id}-rejected`, 'source', Position.Right, { top: '70%' })
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Filter"
      handles={handles}
      nodeType="filter"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <label style={commonLabelStyle}>
          Condition:
          <CustomDropdown
            value={condition}
            onChange={handleConditionChange}
            options={conditionOptions}
            className="node-input"
          />
        </label>
        <label style={commonLabelStyle}>
          Value:
          <input
            type="text"
            value={value}
            onChange={handleValueChange}
            className="node-input"
            placeholder="Enter filter value"
          />
        </label>
      </div>
    </BaseNode>
  );
};
