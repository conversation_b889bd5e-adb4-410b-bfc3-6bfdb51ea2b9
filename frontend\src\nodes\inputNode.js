// inputNode.js

import { useState } from 'react';
import { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';
import { NodeInput } from '../components/NodeInput';
import { CustomDropdown } from '../components/CustomDropdown';

export const InputNode = ({ id, data }) => {
  const [currName, setCurrName] = useState(data?.inputName || id.replace('customInput-', 'input_'));
  const [inputType, setInputType] = useState(data.inputType || 'Text');

  const handleNameChange = (e) => {
    setCurrName(e.target.value);
  };

  const handleTypeChange = (e) => {
    setInputType(e.target.value);
  };

  const typeOptions = [
    { value: 'Text', label: 'Text' },
    { value: 'File', label: 'File' }
  ];

  const handles = [HANDLE_CONFIGS.sourceRight(`${id}-value`)];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Input"
      handles={handles}
      nodeType="input"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <label style={commonLabelStyle}>
          Name:
          <NodeInput
            value={currName}
            onChange={handleNameChange}
            placeholder="Enter input name"
          />
        </label>
        <label style={commonLabelStyle}>
          Type:
          <CustomDropdown
            value={inputType}
            onChange={handleTypeChange}
            options={typeOptions}
            className="node-input"
          />
        </label>
      </div>
    </BaseNode>
  );
}
