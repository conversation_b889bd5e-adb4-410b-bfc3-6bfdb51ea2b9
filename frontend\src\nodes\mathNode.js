// mathNode.js
// Demonstrates mathematical operations with multiple inputs

import { useState } from 'react';
import { BaseNode, createHandle, commonLabelStyle } from './BaseNode';
import { Position } from 'reactflow';
import { CustomDropdown } from '../components/CustomDropdown';

export const MathNode = ({ id, data }) => {
  const [operation, setOperation] = useState(data?.operation || 'add');

  const handleOperationChange = (e) => {
    setOperation(e.target.value);
  };

  const operationOptions = [
    { value: 'add', label: 'Add' },
    { value: 'subtract', label: 'Subtract' },
    { value: 'multiply', label: 'Multiply' },
    { value: 'divide', label: 'Divide' }
  ];

  const handles = [
    createHandle(`${id}-input1`, 'target', Position.Left, { top: '25%' }),
    createHandle(`${id}-input2`, 'target', Position.Left, { top: '75%' }),
    createHandle(`${id}-result`, 'source', Position.Right)
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Math"
      handles={handles}
      nodeType="math"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <label style={commonLabelStyle}>
          Operation:
          <CustomDropdown
            value={operation}
            onChange={handleOperationChange}
            options={operationOptions}
            className="node-input"
          />
        </label>
        <div style={{
          fontSize: '10px',
          color: 'rgba(255, 255, 255, 0.6)',
          fontStyle: 'italic',
          textAlign: 'center',
          padding: '4px 8px',
          backgroundColor: 'rgba(138, 43, 226, 0.1)',
          borderRadius: '4px',
          border: '1px solid rgba(138, 43, 226, 0.2)'
        }}>
          Performs {operation} operation
        </div>
      </div>
    </BaseNode>
  );
};
