// outputNode.js

import { useState } from 'react';
import { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';
import { NodeInput } from '../components/NodeInput';
import { CustomDropdown } from '../components/CustomDropdown';

export const OutputNode = ({ id, data }) => {
  const [currName, setCurrName] = useState(data?.outputName || id.replace('customOutput-', 'output_'));
  const [outputType, setOutputType] = useState(data.outputType || 'Text');

  const handleNameChange = (e) => {
    setCurrName(e.target.value);
  };

  const handleTypeChange = (e) => {
    setOutputType(e.target.value);
  };

  const typeOptions = [
    { value: 'Text', label: 'Text' },
    { value: 'File', label: 'Image' }
  ];

  const handles = [HANDLE_CONFIGS.targetLeft(`${id}-value`)];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Output"
      handles={handles}
      nodeType="output"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <label style={commonLabelStyle}>
          Name:
          <NodeInput
            value={currName}
            onChange={handleNameChange}
            placeholder="Enter output name"
          />
        </label>
        <label style={commonLabelStyle}>
          Type:
          <CustomDropdown
            value={outputType}
            onChange={handleTypeChange}
            options={typeOptions}
            className="node-input"
          />
        </label>
      </div>
    </BaseNode>
  );
}
