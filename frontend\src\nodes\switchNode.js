// switchNode.js
// Demonstrates conditional routing with multiple outputs

import { useState } from 'react';
import { BaseNode, createHandle } from './BaseNode';
import { Position } from 'reactflow';
import { CustomDropdown } from '../components/CustomDropdown';

export const SwitchNode = ({ id, data }) => {
  const [condition, setCondition] = useState(data?.condition || 'true');

  const handleConditionChange = (e) => {
    setCondition(e.target.value);
  };

  const conditionOptions = [
    { value: 'true', label: 'True' },
    { value: 'false', label: 'False' }
  ];

  const handles = [
    createHandle(`${id}-input`, 'target', Position.Left),
    createHandle(`${id}-condition`, 'target', Position.Top),
    createHandle(`${id}-true`, 'source', Position.Right, { top: '30%' }),
    createHandle(`${id}-false`, 'source', Position.Right, { top: '70%' })
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Switch"
      handles={handles}
      nodeType="switch"
      height={90}
    >
      <div>
        <label className="inline-label">
          Default:
          <CustomDropdown
            value={condition}
            onChange={handleConditionChange}
            options={conditionOptions}
            className="node-input"
          />
        </label>
        <div style={{ fontSize: '10px', color: 'rgba(255, 255, 255, 0.6)' }}>
          Routes based on condition
        </div>
      </div>
    </BaseNode>
  );
};
