// timerNode.js
// Demonstrates a timer/delay node with configurable duration

import { useState } from 'react';
import { BaseNode, HANDLE_CONFIGS, inlineLabelStyle } from './BaseNode';
import { CustomDropdown } from '../components/CustomDropdown';

export const TimerNode = ({ id, data }) => {
  const [duration, setDuration] = useState(data?.duration || 1000);
  const [unit, setUnit] = useState(data?.unit || 'ms');

  const handleDurationChange = (e) => {
    setDuration(parseInt(e.target.value) || 0);
  };

  const handleUnitChange = (e) => {
    setUnit(e.target.value);
  };

  const unitOptions = [
    { value: 'ms', label: 'ms' },
    { value: 's', label: 's' },
    { value: 'm', label: 'm' }
  ];

  const handles = [
    HANDLE_CONFIGS.targetLeft(`${id}-trigger`),
    HANDLE_CONFIGS.sourceRight(`${id}-delayed`)
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Timer"
      handles={handles}
      nodeType="timer"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        {/* Inline grouped Duration and Unit */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          marginBottom: '10px'
        }}>
          <label style={inlineLabelStyle}>
            Duration:
            <input
              type="number"
              value={duration}
              onChange={handleDurationChange}
              className="node-input"
              style={{
                width: '80px',
                marginLeft: '4px'
              }}
              min="0"
              step="1"
            />
          </label>
          <label style={inlineLabelStyle}>
            Unit:
            <CustomDropdown
              value={unit}
              onChange={handleUnitChange}
              options={unitOptions}
              style={{
                width: '60px',
                marginLeft: '4px'
              }}
            />
          </label>
        </div>

        {/* Helper text */}
        <div style={{
          fontSize: '10px',
          color: 'rgba(255, 255, 255, 0.6)',
          fontStyle: 'italic',
          textAlign: 'center'
        }}>
          Delays execution by {duration} {unit}
        </div>
      </div>
    </BaseNode>
  );
};
