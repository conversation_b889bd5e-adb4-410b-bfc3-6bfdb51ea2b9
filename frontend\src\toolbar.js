// toolbar.js
import { useState, useEffect } from 'react';

export const PipelineToolbar = () => {
    const [screenWidth, setScreenWidth] = useState(window.innerWidth);

    useEffect(() => {
        const handleResize = () => setScreenWidth(window.innerWidth);
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const getResponsiveTitle = () => {
        if (screenWidth <= 360) return 'VectorShift';
        if (screenWidth <= 480) return 'VectorShift Builder';
        return 'VectorShift Pipeline Builder';
    };
    const onDragStart = (event, nodeType) => {
        const appData = { nodeType };
        event.target.style.cursor = 'grabbing';
        event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));
        event.dataTransfer.effectAllowed = 'move';
    };

    const onDragEnd = (event) => {
        event.target.style.cursor = 'grab';
    };

    const nodeTypes = [
        { type: 'customInput', label: 'Input', color: '#8b5cf6' },
        { type: 'llm', label: 'LLM', color: '#6366f1' },
        { type: 'customOutput', label: 'Output', color: '#8b5cf6' },
        { type: 'text', label: 'Text', color: '#10b981' },
        { type: 'math', label: 'Math', color: '#3b82f6' },
        { type: 'filter', label: 'Filter', color: '#f59e0b' },
        { type: 'timer', label: 'Timer', color: '#ef4444' },
        { type: 'switch', label: 'Switch', color: '#06b6d4' },
        { type: 'aggregator', label: 'Aggregator', color: '#ec4899' }
    ];

    return (
        <div className="pipeline-node-toolbar">
            <div className="node-toolbar-content">
                <div className="toolbar-title">
                    <span>{getResponsiveTitle()}</span>
                </div>
                <div className="toolbar-nodes">
                    {nodeTypes.map((node) => (
                        <div
                            key={node.type}
                            className="draggable-node"
                            draggable
                            onDragStart={(event) => onDragStart(event, node.type)}
                            onDragEnd={onDragEnd}
                            style={{
                                cursor: 'grab'
                            }}
                        >
                            {node.label}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};
